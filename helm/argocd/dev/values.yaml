image:
  repository: fips-registry.us-east-1.build.socure.link/idp/watchlist-in-house-ingestion
  tag: "OVERRIDE_ME"

serviceAccount:
  name: "watchlist-in-house-ingestion-dev"
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/eks-irsa-4699c9c-dev-6860e6f8"

application:
  env:
    CONFIGURATION_NAME: "watchlist-in-house-ingestion"
    CONFIGURATION_VERSION: "0.0.006"
    JAVA_TOOL_OPTIONS: "-Xms16000m -Xmx16000m -XX:InitialRAMPercentage=50.0 -XX:MaxRAMPercentage=85.0 -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Dorg.bouncycastle.jca.enable_jks=true -javaagent:/opt/socure/dd-java-agent-1.18.1.jar -Dnet.spy.log.LoggerImpl=net.spy.memcached.compat.log.SLF4JLogger"

deployment:
  replicaCount: 0
  healthProbes:
    enabled: true
  restartPolicy: Never
  resources:
    limits:
      ephemeral-storage: 16Gi
      memory: 16Gi
      cpu: '4'
    requests:
      cpu: '4'
      ephemeral-storage: 16Gi
      memory: 16Gi
  securityContext:
    readOnlyRootFilesystem: true

autoscaling:
  enabled: false
  minReplicas: 0
  maxReplicas: 0

istio:
  enabled: true
  hosts:
    - watchlist-in-house-ingestion.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  authorizationPolicy:
    enabled: true
    pa:
      enabled: true
      serviceAccounts:
        - senzing-service-dev
        - watchlist-private-dev
        - watchlist-audit-manager-dev
