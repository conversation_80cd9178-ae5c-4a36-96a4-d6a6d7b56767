package me.socure.watchlist.in_house.ingestion.data.extractor.service

import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.model.{GetObjectRequest, ObjectMetadata, PutObjectRequest, PutObjectResult, S3Object}
import com.google.inject.Inject

import java.io.File
import scala.collection.JavaConverters.asScalaBufferConverter

class S3Service @Inject()(s3Client: AmazonS3Client) {

  def doesFileExist(bucketName: String, key: String): Boolean = {
    s3Client.doesObjectExist(bucketName, key)
  }

  def downloadS3ObjectToFile(bucketName: String, s3Path: String, filePath: String): ObjectMetadata = {
    s3Client.getObject(new GetObjectRequest(bucketName, s3Path), new File(filePath))
  }

  def getS3Object(bucketName: String, s3Path: String): S3Object = {
    s3Client.getObject(new GetObjectRequest(bucketName, s3Path))
  }

  def listSortedFolderContents(bucketName: String, prefix: String): List[String] = {
    s3Client.listObjects(bucketName, prefix)
      .getObjectSummaries
      .asScala
      .map(_.getKey.stripPrefix(prefix).split("/")(0))
      .toList
      .sorted
  }

  def deleteObjectsWithPrefix(bucketName: String, prefix: String): Unit = {
    s3Client.listObjects(bucketName, prefix)
      .getObjectSummaries
      .asScala
      .foreach(obj => {
        s3Client.deleteObject(bucketName, obj.getKey)
      })
  }

  def uploadFileToS3(bucketName: String, inputPath: String, outputPath: String): PutObjectResult = {
    val putObjectRequest = new PutObjectRequest(
      bucketName,
      outputPath,
      new File(inputPath)
    )
    s3Client.putObject(putObjectRequest)
  }

}
