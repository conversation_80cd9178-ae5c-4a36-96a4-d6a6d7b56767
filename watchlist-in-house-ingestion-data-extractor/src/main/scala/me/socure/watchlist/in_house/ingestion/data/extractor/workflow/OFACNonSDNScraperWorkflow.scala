package me.socure.watchlist.in_house.ingestion.data.extractor.workflow

import com.google.inject.Inject
import com.typesafe.config.Config
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.watchlist.in_house.ingestion.common.models.enums.WLDataExtractionStatus.WLDataExtractionStatus
import me.socure.watchlist.in_house.ingestion.common.models.enums.{WLDataExtractionStatus, WatchlistSources}
import me.socure.watchlist.in_house.ingestion.data.extractor.common.Constant._
import me.socure.watchlist.in_house.ingestion.data.extractor.service.S3Service
import me.socure.watchlist.in_house.ingestion.data.extractor.utility.TransformationUtil
import org.json4s.{DefaultFormats, Formats}
import org.slf4j.LoggerFactory

import java.text.SimpleDateFormat
import scala.concurrent.ExecutionContext
import scala.io.Source

class OFACNonSDNScraperWorkflow @Inject()(config: Config, s3Service: S3Service)(implicit ec: ExecutionContext) extends StandardScraperWorkflow(config, s3Service) {

  private val logger = LoggerFactory.getLogger(classOf[OFACNonSDNScraperWorkflow])
  private val metrics = JavaMetricsFactory.get("watchlist.in.house.ingestion.data.extractor.workflow")
  private val inHouseS3BucketName = config.getString("watchlist.in.house.s3.bucketName")

  override implicit def formats: Formats = DefaultFormats

  override def performWebScraperPreProcessing(watchlistSource: WatchlistSources.WatchlistSource, runId: String, sourceTag: String): Unit = {
    val lastUpdateS3Object = s3Service.getS3Object(inHouseS3BucketName, s"${oFACNonSDNFilesS3Path}/${lastUpdateJsonFile}")
    val lastUpdateMap = TransformationUtil.getMapFromJsonS3Object(lastUpdateS3Object)

    val lastUpdatedTimestamp = new SimpleDateFormat(M_d_yyyy_h_mm_ss_a_format).parse(lastUpdateMap.get("Non-SDN List last updated on").get)
    val lastUpdatedTimestampArray =  new SimpleDateFormat(M_d_yyyy_h_mm_ss_a_format).format(lastUpdatedTimestamp).split(" ")
    val formattedLastUpdatedTimestamp = (lastUpdatedTimestampArray.apply(0) +'_'+lastUpdatedTimestampArray.apply(1)+lastUpdatedTimestampArray.apply(2)).replace("/","-")
    val yyyyMMddHHMMFormattedTimestamp = new SimpleDateFormat(yyyy_M_d_HH_mm_format).format(lastUpdatedTimestamp)

    s3Service.downloadS3ObjectToFile(inHouseS3BucketName, s"${oFACNonSDNFilesS3Path}/${lastUpdateJsonFile}", s"${oFACNonSDNFilesInputPath}/${lastUpdateJsonFile}")
    s3Service.downloadS3ObjectToFile(inHouseS3BucketName, s"${oFACNonSDNFilesS3Path}/OFAC_NON_SDN_Entities_${formattedLastUpdatedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesInputPath}/OFAC_NON_SDN_Entities_${formattedLastUpdatedTimestamp}.${parquetFileExtension}")
    s3Service.downloadS3ObjectToFile(inHouseS3BucketName, s"${oFACNonSDNFilesS3Path}/OFAC_NON_SDN_Individuals_${formattedLastUpdatedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesInputPath}/OFAC_NON_SDN_Individuals_${formattedLastUpdatedTimestamp}.${parquetFileExtension}")
    s3Service.downloadS3ObjectToFile(inHouseS3BucketName, s"${oFACNonSDNFilesS3Path}/ofac_non_sdn_list_entity_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesInputPath}/ofac_non_sdn_list_entity_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}")
    s3Service.downloadS3ObjectToFile(inHouseS3BucketName, s"${oFACNonSDNFilesS3Path}/ofac_non_sdn_list_indi_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesInputPath}/ofac_non_sdn_list_indi_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}")
  }

  override def performWebScraperPostProcessing(watchlistSource: WatchlistSources.WatchlistSource, runId: String, sourceTag: String, wlDataExtractionStatus: WLDataExtractionStatus): Unit = {
    if (wlDataExtractionStatus == WLDataExtractionStatus.DATA_EXTRACTION_SUCCEEDED) {
      val lastUpdateMap = TransformationUtil.getMapFromJsonFileStream(Source.fromFile(s"${oFACNonSDNFilesInputPath}/${lastUpdateJsonFile}"))
      val lastUpdatedTimestamp = new SimpleDateFormat(M_d_yyyy_h_mm_ss_a_format).parse(lastUpdateMap.get("Non-SDN List last updated on").get)
      val lastUpdatedTimestampArray = new SimpleDateFormat(M_d_yyyy_h_mm_ss_a_format).format(lastUpdatedTimestamp).split(" ")
      val formattedLastUpdatedTimestamp = (lastUpdatedTimestampArray.apply(0) + '_' + lastUpdatedTimestampArray.apply(1) + lastUpdatedTimestampArray.apply(2)).replace("/", "-")
      val yyyyMMddHHMMFormattedTimestamp = new SimpleDateFormat(yyyy_M_d_HH_mm_format).format(lastUpdatedTimestamp)
      s3Service.uploadFileToS3(inHouseS3BucketName, s"${oFACNonSDNFilesInputPath}/OFAC_NON_SDN_Entities_${formattedLastUpdatedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesS3Path}/OFAC_NON_SDN_Entities_${formattedLastUpdatedTimestamp}.${parquetFileExtension}")
      s3Service.uploadFileToS3(inHouseS3BucketName, s"${oFACNonSDNFilesInputPath}/OFAC_NON_SDN_Individuals_${formattedLastUpdatedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesS3Path}/OFAC_NON_SDN_Individuals_${formattedLastUpdatedTimestamp}.${parquetFileExtension}")
      s3Service.uploadFileToS3(inHouseS3BucketName, s"${oFACNonSDNFilesInputPath}/ofac_non_sdn_list_entity_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesS3Path}/ofac_non_sdn_list_entity_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}")
      s3Service.uploadFileToS3(inHouseS3BucketName, s"${oFACNonSDNFilesInputPath}/ofac_non_sdn_list_indi_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}", s"${oFACNonSDNFilesS3Path}/ofac_non_sdn_list_indi_${yyyyMMddHHMMFormattedTimestamp}.${parquetFileExtension}")
      s3Service.uploadFileToS3(inHouseS3BucketName, s"${oFACNonSDNFilesInputPath}/${lastUpdateJsonFile}", s"${oFACNonSDNFilesS3Path}/${lastUpdateJsonFile}")
      logger.info(s"Initiating daily/bulk file S3 file upload for runId ${runId} for the destination ${entityResolutionOutputDataS3Path}/${runId}.json")
      s3Service.uploadFileToS3(
        inHouseS3BucketName,
        s"${entityResolutionInputDataLocalPath}/${watchlistSource.webScraperScriptName}.json",
        s"${entityResolutionOutputDataS3Path}/${runId}.json"
      )
      logger.info(s"S3 file ${entityResolutionOutputDataS3Path}/${runId}.json uploaded successfully")
    }
  }

}
