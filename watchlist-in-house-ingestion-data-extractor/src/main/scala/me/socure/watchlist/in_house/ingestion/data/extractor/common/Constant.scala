package me.socure.watchlist.in_house.ingestion.data.extractor.common

object Constant {

  val dataExtractorScriptPath = "data-extractor/scripts"
  val entityResolutionInputDataLocalPath = "/tmp/entity-resolution/input/senzing"
  val entityResolutionOutputDataS3Path = "entity-resolution/senzing/input"
  val dataExtractorOutputPath = "/tmp/data-extractor/output"
  val masterFilePath = "data-extractor/master-files"
  val oFACNonSDNFilesS3Path = "data-extractor/ofac-non-sdn-files"
  val oFACSDNFilesS3Path = "data-extractor/ofac-sdn-files"
  val oFACNonSDNFilesInputPath = "/tmp/entity-resolution/input/ofac_non_sdn_list"
  val oFACSDNFilesInputPath = "/tmp/entity-resolution/input/ofac_sdn_list"
  val lastUpdateJsonFile = "last_update.json"
  val parquetFileExtension = "parquet"
  val s3DataExtractorPath = "data-extractor"
  val localDataExtractorPath = "/tmp/data-extractor"
  val dataLoaderLocalPath = "data-loader"
  val TransformerJobLocalPath = "/tmp/transformer"
  val scrapperResultsFolder = "scraper-results"

  val ID_PLUS_FEATURES_GROUP_NAME = "IdPlusFeatures"
  val IN_HOUSE_SETTINGS_FLAG_NAME = "WatchlistInHouseSettings"
  val DATA_EXTRACTOR_CONFIG_NAME = "dataExtractor"
  val DATA_EXTRACTOR_DEFAULT_OVERRIDE_RESPONSE = None

  val M_d_yyyy_h_mm_ss_a_format = "M/d/yyyy h:mm:ss a"
  val yyyy_M_d_HH_mm_format = "yyyy-MM-dd-HH-mm"
}
