package me.socure.watchlist.in_house.ingestion.storage.dao

import com.github.tototoshi.slick.GenericJodaSupport
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.watchlist.in_house.ingestion.common.formats.WatchlistInHouseIngestionFormats
import me.socure.watchlist.in_house.ingestion.common.models.Constants.MAX_PAGE_SIZE
import me.socure.watchlist.in_house.ingestion.common.models._
import me.socure.watchlist.in_house.ingestion.common.models.enums.WLDataExtractionStatus.WLDataExtractionStatus
import me.socure.watchlist.in_house.ingestion.storage.model._
import me.socure.watchlist.in_house.ingestion.storage.utility.DBMetricsUtility._
import me.socure.watchlist.in_house.ingestion.storage.utility.DbTables
import me.socure.watchlist.in_house.ingestion.common.models.WLInhouseAuditRecords
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.Formats
import org.slf4j.LoggerFactory
import slick.driver.JdbcProfile

import java.sql.{SQLIntegrityConstraintViolationException, Timestamp}
import java.util.TimeZone
import scala.concurrent.{ExecutionContext, Future}
import javax.sql.DataSource
import slick.jdbc.{GetResult, PositionedParameters, SetParameter}
import me.socure.watchlist.in_house.ingestion.common.utility.SourceAuditDateUtil

/**
 * Created by Soorya Prasad on Aug 29, 2023
 */
class WatchlistInhouseIngestionDao(dataSource: DataSource,
                                   val jodaSupport: GenericJodaSupport,
                                   val profile: JdbcProfile,
                                   retryStrategy: RetryStrategy)(implicit ec: ExecutionContext)
  extends WatchlistSourceAuditTable with WatchlistEntitiesAuditTable with WatchlistCurrentRunDetailsTable with WatchlistSuppressedEntitiesTable with WatchlistEntitySourceMappingTable {

  import profile.api._

  private val db = Database.forDataSource(dataSource)
  private var metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)
  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.dbMetricPrefix)
  private val logger = LoggerFactory.getLogger(getClass)

  private val serviceName = "watchlist_in_house_ingestion"
  private val dbName = "watchlist_in_house_ingestion_audit"

  val formats: Formats = WatchlistInHouseIngestionFormats.formats

  DateTimeZone.setDefault(DateTimeZone.UTC)
  TimeZone.setDefault(TimeZone.getTimeZone("UTC"))

  implicit def jodaTimeMapping: BaseColumnType[DateTime] = MappedColumnType.base[DateTime, Timestamp](
    dateTime => new Timestamp(dateTime.getMillis),
    timeStamp => new DateTime(timeStamp.getTime)
  )

  def fetchWLSourceAuditInfoBySourceId(sourceId: Int): Future[Seq[TblWatchlistSourceAudit#TableElementType]] = {
    val fetchWLSourceAuditInfoBySourceIdQuery = wlSourceAuditQuery
      .filter(row => row.wlSourceId === sourceId)
    val selectFuture = db.run(fetchWLSourceAuditInfoBySourceIdQuery.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistSourceAudit),
      tableAction = selectAction,
      queryType = "bySourceId")
  }

  def fetchWLSourceAuditInfoByRunId(runId: String): Future[Option[TblWatchlistSourceAudit#TableElementType]] = {
    val fetchWLSourceAuditInfoByRunIdQuery = wlSourceAuditQuery
      .filter(row => row.runId === runId)
    val selectFuture = db.run(fetchWLSourceAuditInfoByRunIdQuery.result.headOption)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistSourceAudit),
      tableAction = selectAction,
      queryType = "byRunId")
  }

  def fetchSuppressedEntities(accountId: Int, environmentTypeId: Int): Future[Seq[TblWatchlistSuppressedEntities#TableElementType]] = {
    val fetchWLSuppressedEntitiesQuery = wlSuppressedEntitiesQuery
      .filter(row => row.accountId === accountId)
      .filter(row => row.environmentTypeId === environmentTypeId)
    val selectFuture = db.run(fetchWLSuppressedEntitiesQuery.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistSuppressedEntities),
      tableAction = selectAction,
      queryType = "byAccountId_EnvTypeId")
  }

  def fetchCurrentRunDetails(runId: String): Future[Option[TblWatchlistCurrentRunDetails#TableElementType]] = {
    val fetchWLSuppressedEntitiesQuery = wlCurrentRunDetailsQuery
      .filter(row => row.runId === runId)
    val selectFuture = db.run(fetchWLSuppressedEntitiesQuery.result.headOption)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = selectAction,
      queryType = "byRunId")
  }

  def fetchAllRunDetailsByStatuses(statuses: Set[WLDataExtractionStatus], limit: Int): Future[Seq[TblWatchlistCurrentRunDetails#TableElementType]] = {
    val fetchWLSuppressedEntitiesQuery = constructRunDetailsQuery(statuses, limit)
    val selectFuture = db.run(fetchWLSuppressedEntitiesQuery.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = selectAction,
      queryType = "byStatuses")
  }


  def fetchAllRunDetailsByStatuses(status: Set[WLDataExtractionStatus]): Future[Seq[TblWatchlistCurrentRunDetails#TableElementType]] = {
    val fetchWLSuppressedEntitiesQuery = constructRunDetailsByStatusesQuery(status)
    val selectFuture = db.run(fetchWLSuppressedEntitiesQuery.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = selectAction,
      queryType = "byStatus")
  }




  def modifyWLDataExtractionStatus(status: WLDataExtractionStatus, runIds: Seq[String]): Future[Int] = {
    logger.info(s"${runIds}    ${status} ")
    val modifyWLSuppressedEntitiesQuery = wlCurrentRunDetailsQuery
      .filter(row => row.runId inSetBind runIds.toSet)
      .sortBy(_.created_at)
      .map(row => (row.status, row.updated_at))
      .update(status, DateTime.now())
    val selectFuture = db.run(modifyWLSuppressedEntitiesQuery)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = updateAction,
      queryType = "byStatusAndRunId")
  }

  def modifyWLDataExtractionStatusForRunIds(status: WLDataExtractionStatus, runIds: Seq[String]): Future[Int] = {
    val modifyWLSuppressedEntitiesQuery = wlCurrentRunDetailsQuery
      .filter(row => row.runId inSetBind runIds.toSet)
      .sortBy(_.created_at)
      .map(row => (row.status, row.updated_at))
      .update(status, DateTime.now())
    val selectFuture = db.run(modifyWLSuppressedEntitiesQuery)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = updateAction,
      queryType = "byStatusAndRunId")
  }

  def fetchWLEntitiesAuditInfoByEntityId(entityId: String, fetchLastAudit: Boolean): Future[Seq[TblWatchlistEntitiesAudit#TableElementType]] = {
    val fetchWLEntitiesAuditInfoByEntityIdQuery = wlEntitiesAuditQuery
      .filter(row => row.entityId === entityId)
    val filteredFetchWLEntitiesAuditInfoByEntityIdQuery = if (fetchLastAudit) {
      fetchWLEntitiesAuditInfoByEntityIdQuery.sortBy(_.lastUpdatedTimestamp.desc).take(1)
    } else {
      fetchWLEntitiesAuditInfoByEntityIdQuery
    }
    val selectFuture = db.run(filteredFetchWLEntitiesAuditInfoByEntityIdQuery.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistEntitiesAudit),
      tableAction = selectAction,
      queryType = "byEntityId")
  }

  def fetchAuditRecordsByDateRange(
                                    startDateOpt: Option[String],
                                    endDateOpt: Option[String],
                                    sourceId: Option[Int],
                                    entitySourceChangeType: Option[Int],
                                    runId: Option[String],
                                    offset: Option[Int],
                                    size: Option[Int]
                                  ): Future[List[WLInhouseAuditRecords]] = {

    implicit val convertResult: GetResult[WLInhouseAuditRecords] = GetResult(r =>
      WLInhouseAuditRecords(r.nextInt(), r.nextString(), r.nextString(), r.nextInt(), r.nextInt(), r.nextString())
    )

    val queryDates: (String, String) = (startDateOpt, endDateOpt) match {
      case (Some(startDate), Some(endDate)) =>
        val formattedDate = SourceAuditDateUtil.formatDate(startDate, endDate)
        (formattedDate._1, formattedDate._2)
      case _ =>
        SourceAuditDateUtil.getLast30DaysStartAndEnd
    }

    val whereClauses = List(
      Some(s"twea.last_updated_timestamp >= '${queryDates._1}'"),
      Some(s"twea.last_updated_timestamp <= '${queryDates._2}'"),
      sourceId.map(id => s"twea.source_id = $id"),
      entitySourceChangeType.map(ct => s"twea.entity_source_change_type = $ct"),
      runId.map(rid => s"twcrd.run_id = '$rid'")
    ).flatten.mkString(" AND ")

    val mainQuery =
      s"""
    SELECT
        twcrd.source_id AS watchlistSourceId,
        twcrd.created_at AS dataLoadStartTimestamp,
        twea.last_updated_timestamp AS dataLoadEndTimestamp,
        twea.entity_source_change_type AS entityChangeType,
        COUNT(*) AS entityChangeTypeCount,
        twcrd.run_id
    FROM tbl_wl_current_run_details twcrd
    JOIN tbl_wl_entities_audit twea ON twcrd.run_id = twea.run_id
    WHERE $whereClauses
    GROUP BY twcrd.run_id, twea.entity_source_change_type
    ${paginationClause(offset, size)}
    """

    val dbIOQuery = sql"""#$mainQuery""".as[WLInhouseAuditRecords]

    logger.info(s"Executing raw SQL query: " + dbIOQuery.statements.mkString)

    val fetchAuditRecordsFuture = db.run(dbIOQuery).map(_.toList)

    withMetrics(
      fetchAuditRecordsFuture,
      tableNames = Set(DbTables.TblWatchlistEntitiesAudit, DbTables.TblWatchlistCurrentRunDetails),
      tableAction = selectAction,
      queryType = "with_WatchlistEntitiesAudit_Record_And_WatchlistCurrentRunDetails_Record"
    )

    fetchAuditRecordsFuture.recoverWith {
      case ex: SQLIntegrityConstraintViolationException =>
        logger.error(
          s"SQL Constraint Violation while fetching from WatchlistEntitiesAudit & WatchlistCurrentRunDetails. " +
            s"Class: ${ex.getClass.getSimpleName}, Error: ${ex.getMessage}", ex
        )
        metrics.increment("select.sql.error", s"start_date:$startDateOpt end_date:$endDateOpt")
        Future.failed(ex)

      case ex: Exception =>
        logger.error(
          s"Unknown SQL Error while fetching from WatchlistEntitiesAudit & WatchlistCurrentRunDetails. " +
            s"Class: ${ex.getClass.getSimpleName}, Error: ${ex.getMessage}", ex
        )
        metrics.increment("insert.sql_unknown.error", s"start_date:$startDateOpt end_date:$endDateOpt")
        Future.failed(ex)
    }
  }

  private def paginationClause(offset: Option[Int], size: Option[Int]): String = {
    (size, offset) match {
      case (Some(s), Some(o)) => s"LIMIT $s OFFSET $o"
      case (Some(s), None)    => s"LIMIT $s"
      case _                  => ""
    }
  }

  def insertWLSourceAuditRecord(watchlistSourceAuditRecord: WatchlistSourceAudit): Future[Long] = {
    val insertQuery = createWatchlistSourceAuditQuery(watchlistSourceAuditRecord)
    val insertFuture = db.run(insertQuery)
    withMetrics(insertFuture,
      tableNames = Set(DbTables.TblWatchlistSourceAudit),
      tableAction = insertAction,
      queryType = "with_WatchlistSourceAudit_Record")
    insertFuture recoverWith {
      case ex: SQLIntegrityConstraintViolationException =>
        logger.error("SQL Constraint Violation error while inserting into WatchlistSourceAudit table", ex)
        metrics.increment("insert.sql.error", s"watchlistSourceId:${watchlistSourceAuditRecord.watchlistSourceId}")
        Future.failed(ex)
      case ex: Exception =>
        logger.error("SQL Unknown Error while inserting into WatchlistSourceAudit table", ex)
        metrics.increment("insert.sql_unknown.error", s"watchlistSourceId:${watchlistSourceAuditRecord.watchlistSourceId}")
        Future.failed(ex)
    }
  }

  def insertWLEntitiesAuditRecord(watchlistEntitiesAuditRecord: WatchlistEntitiesAuditRecord): Future[Long] = {
    val insertQuery = createWatchlistEntitiesAuditQuery(watchlistEntitiesAuditRecord)
    val insertFuture = db.run(insertQuery)
    withMetrics(insertFuture,
      tableNames = Set(DbTables.TblWatchlistEntitiesAudit),
      tableAction = insertAction,
      queryType = "with_WatchlistEntitiesAudit_Record")
    insertFuture recoverWith {
      case ex: SQLIntegrityConstraintViolationException =>
        logger.error(s"SQL Constraint Violation error while inserting into WatchlistEntitiesAudit table entityId:${watchlistEntitiesAuditRecord.entityId}", ex)
        metrics.increment("insert.sql.error")
        Future.failed(ex)
      case ex: Exception =>
        logger.error("SQL Unknown Error while inserting into WatchlistEntitiesAudit table", ex)
        metrics.increment("insert.sql_unknown.error")
        Future.failed(ex)
    }
  }

  def fetchAllEntityAuditRecords(entityId: String, lastUpdatedRunId: String): Future[Seq[TblWatchlistEntitiesAudit#TableElementType]] = {
    val fetchWLSuppressedEntitiesQuery = constructEntityAuditQuery(entityId = entityId, lastUpdatedRunId = lastUpdatedRunId)
    val selectFuture = db.run(fetchWLSuppressedEntitiesQuery.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistEntitiesAudit),
      tableAction = selectAction,
      queryType = "byEntityIdAndRunId")
  }

  def insertWLSuppressedEntitiesRecord(watchlistSuppressedEntitiesRecord: WatchlistSuppressedEntities): Future[Long] = {
    val insertQuery = createWatchlistSuppressedEntitiesQuery(watchlistSuppressedEntitiesRecord)
    val insertFuture = db.run(insertQuery)
    withMetrics(insertFuture,
      tableNames = Set(DbTables.TblWatchlistSuppressedEntities),
      tableAction = insertAction,
      queryType = "with_WatchlistSuppressedEntities_Record")
    insertFuture recoverWith {
      case ex: SQLIntegrityConstraintViolationException =>
        logger.error("SQL Constraint Violation error while inserting into WatchlistSuppressedEntities table", ex)
        metrics.increment("insert.sql.error", s"accountId:${watchlistSuppressedEntitiesRecord.accountId}", s"envTypeId:${watchlistSuppressedEntitiesRecord.environmentTypeId}")
        Future.failed(ex)
      case ex: Exception =>
        logger.error("SQL Unknown Error while inserting into WatchlistSuppressedEntities table", ex)
        metrics.increment("insert.sql_unknown.error", s"accountId:${watchlistSuppressedEntitiesRecord.accountId}", s"envTypeId:${watchlistSuppressedEntitiesRecord.environmentTypeId}")
        Future.failed(ex)
    }
  }

  def insertWLCurrentRunDetailsRecord(wLDataExtractionRunDetailsRecord: WLDataExtractionRunDetails): Future[Long] = {
    val insertQuery = createWatchlistCurrentRunDetailsQuery(wLDataExtractionRunDetailsRecord)
    val insertFuture = db.run(insertQuery)
    withMetrics(insertFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = insertAction,
      queryType = "with_WatchlistCurrentRunDetails_Record")
    insertFuture recoverWith {
      case ex: SQLIntegrityConstraintViolationException =>
        logger.error("SQL Constraint Violation error while inserting into WLDataExtractionRunDetails table", ex)
        metrics.increment("insert.sql.error", s"sourceId:${wLDataExtractionRunDetailsRecord.sourceId}")
        Future.failed(ex)
      case ex: Exception =>
        logger.error("SQL Unknown Error while inserting into WLDataExtractionRunDetails table", ex)
        metrics.increment("insert.sql_unknown.error", s"sourceId:${wLDataExtractionRunDetailsRecord.sourceId}")
        Future.failed(ex)
    }
  }

  def insertWLEntitySourceMappingRecord(wLEntitySourceMappingRecord: WatchlistEntitySourceMapping): Future[Long] = {
    val insertQuery = createWatchlistEntitySourceMappingQuery(wLEntitySourceMappingRecord)
    val insertFuture = db.run(insertQuery)
    withMetrics(insertFuture,
      tableNames = Set(DbTables.TblWatchlistEntitySourceMapping),
      tableAction = insertAction,
      queryType = "with_WatchlistEntitySourceMapping_Record")
    insertFuture recoverWith {
      case ex: SQLIntegrityConstraintViolationException =>
        logger.error(s"SQL Constraint Violation error while inserting into WatchlistEntitySourceMapping table. entityId:${wLEntitySourceMappingRecord.entityId}", ex)
        metrics.increment("insert.sql.error", s"sourceId:${wLEntitySourceMappingRecord.sourceId}")
        Future.failed(ex)
      case ex: Exception =>
        logger.error("SQL Unknown Error while inserting into WatchlistEntitySourceMapping table", ex)
        metrics.increment("insert.sql_unknown.error",  s"sourceId:${wLEntitySourceMappingRecord.sourceId}")
        Future.failed(ex)
    }
  }

  def getLastRunDetailsForSourceId(sourceId: Int): Future[Seq[WLDataExtractionRunDetails]] = {
    val inProgressSources = wlCurrentRunDetailsQuery
      .filter(_.sourceId === sourceId)
      .sortBy(_.id.desc)
      .take(1)
    val selectFuture = db.run(inProgressSources.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = selectAction,
      queryType = "getLastRunDetailsForSourceId")
  }

  def updateCurrentRunDetailsForRunId(runId: String, status: WLDataExtractionStatus): Future[Boolean] = {
    val updateQuery = wlCurrentRunDetailsQuery
      .filter(row => row.runId === runId)
      .map(row => (row.status, row.updated_at))
      .update((status, DateTime.now()))
    val updateFuture = db.run(updateQuery)
    withMetrics(updateFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = selectAction,
      queryType = "getInProgressRunDetailsForSourceId")
    updateFuture map {
      case size: Int =>
      if (size > 0)
        true
      else
        false
      case _ => false
    }
  }

  def fetchSourceIdsForRunIds(runIds: Set[String]): Future[Seq[(String, Int)]] = {
    val fetchQuery = wlCurrentRunDetailsQuery
      .filter(row => row.runId inSetBind runIds)
      .map(row => (row.runId, row.sourceId))
    val selectFuture = db.run(fetchQuery.result)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = selectAction,
      queryType = "getSourceIdsByRunIds")
  }

  def updateWLRunDetails(status: WLDataExtractionStatus, runId: String): Future[Int] = {
    val updateQuery = wlCurrentRunDetailsQuery
      .filter(row => row.runId === runId)
      .map(row => (row.status, row.updated_at))
      .update(status, DateTime.now(DateTimeZone.UTC))
    val selectFuture = db.run(updateQuery)
    withMetrics(selectFuture,
      tableNames = Set(DbTables.TblWatchlistCurrentRunDetails),
      tableAction = updateAction,
      queryType = "updateWlRunDetails")
  }

  private def createWatchlistSourceAuditQuery(watchlistSourceAudit: WatchlistSourceAudit) = {
    wlSourceAuditQuery returning (wlSourceAuditQuery.map(_.id)) += watchlistSourceAudit
  }

  private def createWatchlistEntitiesAuditQuery(watchlistEntitiesAudit: WatchlistEntitiesAuditRecord) = {
    wlEntitiesAuditQuery returning (wlEntitiesAuditQuery.map(_.id)) += watchlistEntitiesAudit
  }

  private def createWatchlistSuppressedEntitiesQuery(watchlistSuppressedEntities: WatchlistSuppressedEntities) = {
    wlSuppressedEntitiesQuery returning (wlSuppressedEntitiesQuery.map(_.id)) += watchlistSuppressedEntities
  }

  private def createWatchlistCurrentRunDetailsQuery(wlDataExtractionRunDetails: WLDataExtractionRunDetails) = {
    wlCurrentRunDetailsQuery returning (wlCurrentRunDetailsQuery.map(_.id)) += wlDataExtractionRunDetails
  }

  private def createWatchlistEntitySourceMappingQuery(watchlistEntitySourceMapping: WatchlistEntitySourceMapping) = {
    wlEntitySourceMappingQuery returning (wlEntitySourceMappingQuery.map(_.id)) += watchlistEntitySourceMapping
  }

  private def constructRunDetailsQuery(statuses: Set[WLDataExtractionStatus], limit: Int) = {
    wlCurrentRunDetailsQuery
      .filter(row => row.status.inSetBind(statuses))
      .sortBy(_.created_at)
      .take(limit)
  }

  private def constructRunDetailsByStatusesQuery(statuses: Set[WLDataExtractionStatus]) = {
    wlCurrentRunDetailsQuery
      .filter(row => row.status inSetBind statuses)
      .sortBy(_.created_at)
  }

  private def constructEntityAuditQuery(entityId: String, lastUpdatedRunId: String) = {
    wlEntitiesAuditQuery
      .filter(row => row.entityId === entityId && row.lastUpdatedRunId === lastUpdatedRunId )

  }
}
