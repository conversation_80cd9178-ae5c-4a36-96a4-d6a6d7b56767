scheduler {
  interval = 900
  startDelay = 10
  api {
    timeout = 10
  }
}

// data extractor thread pool
data.extractor.threadpool {
  poolSize = 50
}

// worker thread pool
threadpool {
  poolSize = 100
}

server {
  port = 5000
  apiTimeout = "10 minutes"
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 90%
    non_heap.used_max_percentage = "<80.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

jmx {
  port = 1098
}

#============== Watchlist in-house ingestion Hmac =================#

security {
  hmac {
    ttl = 5
    time.interval = 5
    strength = 512
    aws.secrets.manager.id = "watchlist-in-house-ingestion/stage/hmac-d3a18aa6"
    secret.refresh.interval = 5000
  }
}

#============== Watchlist in-house ingestion Hmac =================#

#============== Watchlist in-house ingestion RDS =================#

withDBMigration = true

database {
    user = "rds-watchlist-in-house-stage-d35a82-app"
    driver = com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver
    jdbcUrl = "jdbc-secretsmanager:mysql://watchlist-in-house-stage.cluster-cvuthvaf2kni.us-gov-west-1.rds.amazonaws.com/watchlist_in_house"
    dataSourceName = "watchlist_in_house"
    maxIdleTime = 900
    maxConnectionAge = 3600
    maxPoolSize = 50
    minPoolSize = 3
    initialPoolSize = 3
    testConnectionOnCheckIn = true
    testConnectionOnCheckOut = false
    idleConnectionTestPeriod = 20
}

#============== Watchlist in-house ingestion RDS =================#

#============== Watchlist in-house ingestion SNS =================#
watchlist.in.house.ingestion.sns {
  sns.topic = "wl-ingestion-audit-stage.fifo"
  sns.regions = [
    "us-gov-west-1"
  ]
  s3.bucket.fallback {
    name = "stage-audit-errors-147787095025-us-gov-west-1"
    region = "us-gov-west-1"
    kms {
      id = "arn:aws-us-gov:kms:us-gov-west-1:147787095025:key/d3d39577-64b4-4c70-8d60-b89efe436086"
    }
  }
  retry {
    initial.backoff = "2 seconds"
    max.backoff = "32 seconds"
    multiplier = 2
    max.attempts = 10
  }
}
#============== Watchlist in-house ingestion SNS =================#

#============== Watchlist in-house ingestion SQS =================#

sqs {
  
  primary {
    region = "us-gov-west-1",
    queue = "tf-wl-ingestion-audit-stage.fifo",
    parallelism = 3
  }
  # [TODO] No secondary region for stage, configured using primary
  secondary {
    region = "us-gov-west-1",
    queue = "tf-wl-ingestion-audit-stage.fifo",
    parallelism = 1,
    sleep {
      duration = "15 minutes",
      afterNEmptyMessages = 1,
    }
  }
  # [TODO] No tertiary for stage, configured using primary
  tertiary {
    region = "us-gov-west-1",
    queue = "tf-wl-ingestion-audit-stage.fifo",
    parallelism = 1,
    sleep {
      duration = "15 minutes",
      afterNEmptyMessages = 1,
    }
  }
  visibilityTimeoutInSecs = 300
}

#============== Watchlist in-house ingestion SQS =================#

#=============== SENZING ================#

senzing {
  endpoint = "http://senzing-entity-resolution"
}

#=============== SENZING ================#


#=============== Redis ================#

redis {
  uri = "rediss://stage-wl-in-house-service-001.stage-wl-in-house-service.rrznfx.usgw1.cache.amazonaws.com:6379"
  replica = {
    uri = "rediss://stage-wl-in-house-service-002.stage-wl-in-house-service.rrznfx.usgw1.cache.amazonaws.com:6379"
  }
  lock {
    validityInMills = 5000
    key = "job_schedular"
  }
}

job.scheduler.redis {
  lock {
    validityInMills = 5000
    key = "job_schedular"
    retry {
      initialBackOffInMillis = 100
      maxRetryAttempts = 3
    }
  }
}

wl.in.house.ingestion {
  job {
    maxJobs = 2
    interval.secs = 10
    parallelism = 2
    heartbeat.validity.in.secs = 28800
    redis.lock {
      validity.millis = 5000
      key = "wl_ingestion_sched"
      retry {
        initial.backoff.millis = 100
        max.attempts = 3
      }
    }
  }
}

#=============== Redis ================#

#=============== Dynamo DB =============#
# [TODO] Only "watchlist-in-house-ingestion-stage" exists in terraform state file, can both share the same table?
dynamodb {
  region = "us-gov-west-1"
  billingMode = "PAY_PER_REQUEST"
  entity {
    table = "watchlist-in-house-ingestion-stage"
    name = "watchlist-in-house-ingestion-stage"
    defaultWriteCapacity = 5
    defaultReadCapacity = 5
  }
  batch.size = 25
}
# [TODO] Only "watchlist-in-house-ingestion-stage" exists in terraform state file, can both share the same table?
dynamodb_2 {
  region = "us-gov-west-1"
  entity {
    table = "watchlist-in-house-ingestion-stage"
    name = "watchlist-in-house-ingestion-stage"
  }
  batch.size = 25
}

#=============== Dynamo DB =============#

#============== Watchlist in-house ingestion S3 =================#

entity {
  resolved {
    s3 {
      bucket = "wl-inhouse-data-stage"
      path = "entity-resolution"
    }
  }
}

#============== Watchlist in-house ingestion S3 =================#

#============== Data loader Scheduler =================#
heart.beat.monitoring {
  scheduler.config {
    parallelism = 1
    interval = 5
  }
  job.config {
    maxJobs = 1
  }
}

data.extractor {
  scheduler.config {
    // scheduler speific
    parallelism = 1
    interval = 60
  }
  job.config {
    maxJobs = 1
  }
}

entity.resolution {
  scheduler.config {
    // scheduler speific
    parallelism = 1
    interval = 5
  }
  job.config {
    maxJobs = 1
  }
}

data.loader {
  scheduler.config {
    // scheduler speific
    parallelism = 1
    interval = 5
  }
  job.config {
    maxJobs = 1
  }
}

transformer {
  s3 {
    bucket = "wl-inhouse-data-stage"
    path = "transformer/data"
  }
  scheduler.config {
    // scheduler speific
    parallelism = 1
    interval = 60
  }
  job.config {
    maxJobs = 1
  }
}

change.notifier {
  scheduler.config {
    // scheduler speific
    parallelism = 1
    interval = 60
  }
  job.config {
    maxJobs = 1
  }
}


job.scheduler.thread.count = 3

global.jobs.count = 5

// heart beat config
heartbeatconfig {

}

scheduler {
  data {
    loader {
      interval = "1 minute"
      parallelism = 2
      num.workers.per.scheduler = 3
      block.size = 10
    }
  }

}

#============== Data loader Scheduler =================#

#============== Elastic Search =================#

elasticsearch {
  hostName = "vpc-wl-in-house-stage-zozbo7dqxy7vi3jseueyhj4sfm.us-gov-west-1.es.amazonaws.com"
  port = 443
  scheme = "https"
  batch.size = 100
  index_1 {
      alias = "wl-in-house-ingestion-scrapper-alias"
    }
    index_2 {
        alias = "wl-in-house-ingestion-acuris-alias"
      }

}

#============== Elastic Search =================#

#============== Scheduler =================#

retry.scheduler.threadpool {
  size = 5
}
#============== Scheduler =================#

watchlist.in.house.s3 {
  bucketName = "wl-inhouse-data-stage"
}
watchlist.scrapping {
  apikey = "ENC(FSmoVqJfBETGRrBdo5AhWnSPk22+O64Rs633kIlWcg2rHFZP98tgrDHUmhTAq1oXxv2KTQ1hT+cgn1Z47FvVl8GJutJGlFsFakgS2JMf3Ms/SMUudTYwlK1QELzwZZPuWDNaYLDANl07c2nYF+ZSbuMvoFXq0aovlTpGm/as9IS2ENes/EaHJqXHPhnldGucIwYY6F8n+LrZMvK6bS/axSK99DSTfuK4L2O0hHZUzMzyN+YM1trHaVwuaUUwdnk=)"
}

#===================Dynamic Control Center==========================#
dynamic.control.center {
    s3 {
      bucketName = "globalconfig-147787095025-us-gov-west-1"
    }
    memcached {
      host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
      port=11211
      ttl=86400
    }
    local {
      cache.timeout.minutes=2
    }
  }
#===================Dynamic Control Center==========================#

file.upload {
  uploadMaxSize = 1073741824
  requestMaxSize = 1078984704
}
#=============== Acuris ================#

# [TODO] Not used in govcloud
acuris {

  delta {
    file {
        recordsMaxCount = 5000
    }
    endpoint = " "
    apikey = "watchlist-in-house-ingestion/stage/01c7f2a9"

    updater {
        scheduler.config {
            parallelism = 1
            interval = 15
          }
     job.config {
       maxJobs = 1
     }
   }
  }
}

#=============== Acuris ================#
duplicate_keys_by_source_id.3 =[
           "address 1",
           "address 2",
           "address 3",
           "address 4",
           "address 5",
           "address 6",
           "name 1",
           "name 2",
           "name 3",
           "name 4",
           "name 5",
           "name 6"]
#=============== Acuris ================#

disabledJobs: ["scraper","entityResolution","transformer"]

