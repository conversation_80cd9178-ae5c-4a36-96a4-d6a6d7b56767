# RFJ Rewards Scraper - Usage Guide

## Overview
The RFJ (Rewards for Justice) scraper extracts terrorism reward information from the U.S. State Department's Rewards for Justice website. It follows the standard scraper pattern used by other scrapers in the system.

## Usage

### Basic Usage
```bash
# Run the scraper
python data-extractor/scripts/rfj_rewards.py rfj_rewards <run_id> <is_manual_update>

# Example
python data-extractor/scripts/rfj_rewards.py rfj_rewards prod_run_001 false
```

### Parameters
- `run_id`: Unique identifier for this scraping run
- `is_manual_update`: Set to "true" for manual runs, "false" for automated runs

### Testing Mode
For testing with limited data, uncomment line 474 in the script:
```python
# reward_urls = reward_urls[:5]  # Uncomment to limit for testing
```

## Output Files

The scraper generates these files in `/tmp/entity-resolution/input/senzing/`:

1. **rfj_rewards_latest.parquet** - Structured data in standard format
2. **rfj_rewards.json** - Data formatted for Senzing ingestion  
3. **rfj_rewards_meta_data.txt** - Record count
4. **rfj_rewards_new_master.parquet** - Master file for delta processing

## Data Fields Extracted

### Standard Fields
- **FullName**: Individual's name
- **EntityType**: Always "Individual"
- **Address**: Associated locations
- **Countries**: Country codes from citizenship/locations
- **Alias**: Alternative name spellings
- **ReasonOrSanctionType**: "Terrorism Reward"
- **SourceName**: "Rewards for Justice"
- **SourceCode**: "RFJ"
- **Url**: Source page URL

### Additional Fields
- **Reward Amount**: Text and numeric versions
- **Sex/Gender**: When available
- **Images**: Array of image URLs
- **Posters**: Array of poster URLs
- **Personal Details**: DOB, place of birth, nationality, physical description
- **Organizations**: Associated terrorist organizations

## Features

### ✅ Incremental Updates
- Checks for new data since last run
- Supports delta processing for additions/deletions
- Maintains update timestamps

### ✅ Error Handling
- Robust error handling with detailed logging
- Graceful handling of missing data
- Retry mechanisms for network requests

### ✅ Data Quality
- Automatic country code mapping
- Data validation and cleaning
- Duplicate removal
- Unique ID generation

### ✅ Monitoring
- DataDog metrics integration
- Comprehensive logging
- Success/failure tracking

## Architecture

The scraper follows a two-phase approach:

1. **Listing Phase**: Scrapes all reward listing pages to collect individual reward URLs
2. **Detail Phase**: Fetches each individual reward page to extract detailed information

## Performance

- Processes ~90 rewards (as of current data)
- ~4 seconds per reward page (includes polite delays)
- Total runtime: ~6-8 minutes for full scrape
- Memory efficient with streaming processing

## Maintenance

### Regular Checks
- Monitor for website structure changes
- Verify field extraction accuracy
- Check for new field types

### Troubleshooting
- Check logs for HTTP errors
- Verify CSS selectors still work
- Test with small data sets first

## Integration

The scraper integrates with:
- **Utils.py**: Common utilities for date parsing, country codes, etc.
- **Senzing**: Data output formatted for entity resolution
- **DataDog**: Metrics and monitoring
- **Standard Schema**: Follows the generalized column structure
