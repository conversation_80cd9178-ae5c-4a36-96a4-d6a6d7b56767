stages:
  - update-mscv
  - update-mscv-gov
  - build
  - wizscan
  - deploy

include:
  - local: 'spec/maven.yml'
  - local: 'spec/build-kaniko-gl-ci.yml'
  - project: 'plt/gitlab-pipeline-templates'
    file: jobs/update-argocd-config/update-argocd-config-py.gitlab-ci.yml
  - project: 'plt/gitlab-pipeline-templates'
    file: jobs/update-config/update-mscv-py.gitlab-ci.yml
  - project: 'plt/gitlab-pipeline-templates'
    file: jobs/update-config/update-mscv-gov.gitlab-ci.yml
  - project: 'plt/gitlab-pipeline-templates'
    file: variables/jib-and-socure-services-variables.yml
  - project: 'plt/gitlab-pipeline-templates'
    file:  jobs/publish/wiz-scan.gitlab-ci.yml
    


variables:
  PROJECT_NAME: idp
  SERVICE_NAME: watchlist-in-house-ingestion
  SERVICE_VERSION: 0.0.002
  NAMESPACE: socure-apps
  BUILD_PATH: "."
  BUILD_DOCKERFILE: "Dockerfile"
  MAVEN_EXTRA_ARGS: -Dbuild-environment=gitlab-ci
  MAVEN_FIPS_ARGS: -Dbuild-environment=gitlab-ci-fips
  DEV_ENVIRONMENT: "yes"
  MEND_PRODUCT_NAME: socure-saas
  GOV_STAGE_MSCV_UPDATE: "true"


jib-build-publish-fips-jdk8:
  stage: build
  extends:
    - .build-container
