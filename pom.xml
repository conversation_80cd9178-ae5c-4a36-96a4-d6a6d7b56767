<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>me.socure</groupId>
        <artifactId>maven-root</artifactId>
        <version>0.2-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>watchlist-in-house-ingestion</artifactId>
    <version>${revision}</version>

    <properties>
        <socure.commons.version>${revision}</socure.commons.version>
    </properties>


    <modules>
        <module>watchlist-in-house-ingestion-worker</module>
        <module>watchlist-in-house-ingestion-common</module>
        <module>watchlist-in-house-ingestion-storage</module>
        <module>watchlist-in-house-ingestion-auditing</module>
        <module>watchlist-in-house-ingestion-rest</module>
        <module>acuris-client</module>
        <module>watchlist-in-house-ingestion-client-sns</module>
        <module>watchlist-in-house-senzing-client</module>
        <module>watchlist-in-house-ingestion-data-loader</module>
        <module>watchlist-in-house-ingestion-data-extractor</module>
        <module>watchlist-in-house-ingestion-scheduler</module>
        <module>watchlist-in-house-ingestion-client</module>
    </modules>

    <packaging>pom</packaging>

    <profiles>
        <profile>
            <id>jib-fips</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci-fips</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <allowInsecureRegistries>true</allowInsecureRegistries>
                            <from>
                                <image>${env.JIB_JDK8_FIPS_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>fips-registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}:latest-fips
                                </image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>${env.FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.watchlist.in_house.ingestion.worker.Main</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>

        <profile>
            <id>jib</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <from>
                                <image>amazoncorretto:8u342-al2</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SERVICE_NAME}
                                </image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest</tag>
                                    <tag>${env.NON_FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.watchlist.in_house.ingestion.worker.Main</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>flatten-maven-plugin</artifactId>
                    </plugin>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>properties-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
