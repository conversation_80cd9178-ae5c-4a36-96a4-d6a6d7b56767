package me.socure.watchlist.in_house.ingestion.client

import com.google.inject.Inject
import dispatch.{Http, Req, url}
import me.socure.common.data.core.provider.func2RichDataProviderInvariant
import me.socure.common.http.HttpStatus
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.metrics.models.MetricTags
import me.socure.common.microservice.client.MicroServiceResponseParser
import me.socure.model.ErrorResponse
import me.socure.watchlist.in_house.ingestion.common.formats.WatchlistInHouseIngestionFormats
import me.socure.watchlist.in_house.ingestion.common.models.enums.WatchlistSources.WatchlistSource
import me.socure.watchlist.in_house.ingestion.common.models.{WLInhouseAuditRecords, WatchlistEntitiesAudit}
import org.apache.http.entity.ContentType
import org.json4s.Formats
import org.slf4j.LoggerFactory
import org.asynchttpclient.Response

import java.nio.charset.{Charset, StandardCharsets}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.matching.Regex

class WatchlistInHouseClient @Inject()(http: Http, endpoint: String)(implicit ec: ExecutionContext) {

  private val safeEndpoint = endpoint.stripSuffix("/")
  private val logger = LoggerFactory.getLogger(this.getClass)
  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix)
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[WatchlistInHouseClient])

  implicit val jsonFormats: Formats = WatchlistInHouseIngestionFormats.formats

  private def extractHttpStatus(response: Response): MetricTags = {
    MetricTags(httpStatus = Some(response.getStatusCode))
  }

  private def extractFailureTags(exception: Throwable): MetricTags = {
    MetricTags(tags = Set(s"ex_class:${exception.getClass.getSimpleName}"))
  }


  def getEntityDetails(entityId: String, fetchLastAudit: Boolean = false): Future[Either[ErrorResponse, Seq[WatchlistEntitiesAudit]]] = {
    val apiName = "/api/watchlist/ingestion/auditing/entities"
    val request = url(s"$safeEndpoint$apiName/$entityId?fetchLastAudit=$fetchLastAudit")
      .GET
      .setContentType("application/json", Charset.forName("UTF-8"))

    val httpRequest = http(request)
      
    httpRequest.map { response =>
      val responseStatus = response.getStatusCode
      try {
        MicroServiceResponseParser.parseResponse[Seq[WatchlistEntitiesAudit]](responseStatus, response.getResponseBody)
      } catch {
        case NonFatal(ex) =>
          logger.error(s"Exception while getting entity details for ${entityId} " +
            s" http res ${responseStatus}", ex)
          Left(ErrorResponse(HttpStatus.BAD_REQUEST.value(), ex.getMessage))
      }
    }.recover {
      case NonFatal(ex) =>
        logger.error(s"Exception while getting entity details for ${entityId}", ex)
        Left(ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage))
    }
  }

  def getWatchlistSources(): Future[Either[ErrorResponse, Set[WatchlistSource]]] = {
    val apiName = "/api/watchlist/ingestion/auditing/wlsources"
    val request = url(s"$safeEndpoint$apiName")
      .GET
      .setContentType("application/json", Charset.forName("UTF-8"))

    val httpRequest = http(request)
 
    httpRequest.map { response =>
      val responseStatus = response.getStatusCode
      try {
        MicroServiceResponseParser.parseResponse[Set[WatchlistSource]](responseStatus, response.getResponseBody)
      } catch {
        case NonFatal(ex) =>
          logger.error(s"Exception while getting WL sources " +
            s" http res ${responseStatus}", ex)
          Left(ErrorResponse(HttpStatus.BAD_REQUEST.value(), ex.getMessage))
      }
    }.recover {
      case NonFatal(ex) =>
        logger.error(s"Exception while getting WL sources", ex)
        Left(ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage))
    }
  }

  def getAuditLogDetailsByDateRange(
                                     startDate: Option[String] = None,
                                     endDate: Option[String] = None,
                                     sourceId: Option[Int] = None,
                                     entitySourceChangeType: Option[Int] = None,
                                     runId: Option[String] = None,
                                     page: Option[Int] = None,
                                     size: Option[Int] = None
                                   ): Future[Either[ErrorResponse, Seq[WLInhouseAuditRecords]]] = {
    val apiName = "/api/watchlist/ingestion/auditing/entities_audit"
    val baseUrl = s"$safeEndpoint$apiName"

    val queryParams: Map[String, String] = Map(
      "startDate" -> startDate,
      "endDate" -> endDate,
      "sourceId" -> sourceId.map(_.toString),
      "entitySourceChangeType" -> entitySourceChangeType.map(_.toString),
      "runId" -> runId,
      "page" -> page.map(_.toString),
      "size" -> size.map(_.toString)
    ).collect { case (key, Some(value)) => key -> value }

    val request: Req = (url(baseUrl) <<? queryParams)
      .GET
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

    val httpRequest = http(request)

    httpRequest.map { response =>
      val responseStatus = response.getStatusCode
      try {
        MicroServiceResponseParser.parseResponse[Seq[WLInhouseAuditRecords]](responseStatus, response.getResponseBody)
      } catch {
        case NonFatal(ex) =>
          logger.error("Exception while getting audit log details " +
            s"http res $responseStatus", ex)
          Left(ErrorResponse(HttpStatus.BAD_REQUEST.value(), ex.getMessage))
      }
    }.recover {
      case NonFatal(ex) =>
        logger.error(s"Exception while getting audit log details", ex)
        Left(ErrorResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), ex.getMessage))
    }
  }

}
