import os
import time
import warnings
import logging
import sys
import json
import requests
import pandas as pd
import numpy as np
import re
import traceback
from bs4 import BeautifulSoup
from datetime import datetime
from datadog import initialize, statsd

warnings.simplefilter('ignore')

# Datadog settings (adjust if necessary)
host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)

# Import utils functions
from utils import (
    try_date_parse, get_today, get_current_time, additional_fields,
    create_unique_ids, is_new_update_available, check_for_addition_and_deletion_v2,
    update_for_senzing, data_Path, generalized_cols, get_country_code, get_proxy,
    http_with_retries, logger
)

# ----------------------------
# Part 1: Listing Page Scraper
# ----------------------------
def get_listing_data(page: int) -> dict:
    """
    Sends a POST request to retrieve the listing JSON for a given page number using the correct jet_smart_filters API.
    Returns the JSON dictionary if successful, else None.
    """
    base_url = "https://rewardsforjustice.net/wp-admin/admin-ajax.php"

    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "origin": "https://rewardsforjustice.net",
        "referer": f"https://rewardsforjustice.net/index/?jsf=jet-engine:rewards-grid&tax=crime-category:1072&pagenum={page}",
        "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ),
        "x-requested-with": "XMLHttpRequest"
    }

    # Build the payload based on the actual request format
    payload_data = {
        "action": "jet_smart_filters",
        "provider": "jet-engine/rewards-grid",
        "query[_tax_query_crime-category]": "1072",
        "defaults[post_status][]": "publish",
        "defaults[post_type][]": ["north-korea", "rewards"],
        "defaults[posts_per_page]": "9",
        "defaults[paged]": "1",
        "defaults[ignore_sticky_posts]": "1",
        "settings[lisitng_id]": "22078",
        "settings[columns]": "3",
        "settings[columns_tablet]": "1",
        "settings[columns_mobile]": "1",
        "settings[column_min_width]": "240",
        "settings[inline_columns_css]": "false",
        "settings[post_status][]": "publish",
        "settings[posts_num]": "9",
        "settings[not_found_message]": "No data was found",
        "settings[use_custom_post_types]": "yes",
        "settings[custom_post_types][]": ["north-korea", "rewards"],
        "settings[_element_id]": "rewards-grid",
        "props[found_posts]": "89",
        "props[max_num_pages]": "10",
        "props[page]": str(page),
        "paged": str(page),
        "referrer[uri]": f"/index/?jsf=jet-engine:rewards-grid&tax=crime-category:1072&pagenum={page}",
        "referrer[self]": "/index.php",
        "indexing_filters": "[41852,41851]"
    }

    # Convert to URL-encoded format
    payload_parts = []
    for key, value in payload_data.items():
        if isinstance(value, list):
            for v in value:
                payload_parts.append(f"{key}={requests.utils.quote(str(v))}")
        else:
            payload_parts.append(f"{key}={requests.utils.quote(str(value))}")

    payload = "&".join(payload_parts)

    try:
        session = requests.Session()
        session.verify = False
        response = session.post(base_url, headers=headers, data=payload, timeout=30)
    except requests.RequestException as e:
        logger.error("Error during request on page %d: %s", page, e)
        return None

    content_type = response.headers.get('Content-Type', '')
    if response.ok and "application/json" in content_type:
        try:
            json_data = response.json()
            return json_data
        except Exception as e:
            logger.error("Error parsing JSON response on page %d: %s", page, e)
            return None
    else:
        logger.error("Response is not JSON on page %d. Status: %s", page, response.status_code)
        return None

def get_max_page(json_data: dict) -> int:
    """
    Extracts the maximum page number from the JSON response.
    The new API returns pagination info in json_data['pagination']['max_num_pages'].
    """
    # Check for pagination info in the new API response format
    pagination = json_data.get("pagination", {})
    if pagination and "max_num_pages" in pagination:
        try:
            max_page = int(pagination["max_num_pages"])
            return max_page
        except (ValueError, TypeError):
            pass

    # Fallback: look for data-pages attribute in the HTML content
    content = json_data.get("content", "")
    if content:
        soup = BeautifulSoup(content, 'lxml')
        grid_div = soup.find("div", class_="jet-listing-grid")
        if grid_div and grid_div.has_attr("data-pages"):
            try:
                max_page = int(grid_div["data-pages"])
                return max_page
            except ValueError:
                pass

    # Final fallback
    return 10

def parse_listing_html(html_str: str) -> list:
    """
    Extracts and returns a list of reward page URLs from the listing HTML.
    """
    soup = BeautifulSoup(html_str, 'lxml')
    post_items = soup.find_all("div", class_="jet-listing-grid__item")
    urls = []
    for item in post_items:
        overlay_link = item.find("a", class_="jet-engine-listing-overlay-link")
        post_url = overlay_link["href"] if overlay_link and overlay_link.has_attr("href") else None
        if post_url:
            urls.append(post_url)
    return urls

def fetch_listing_urls() -> list:
    """
    Retrieves all reward URLs from the listing pages using the new API format.
    """
    first_page_json = get_listing_data(page=1)
    if first_page_json is None:
        logger.error("Failed to retrieve listing JSON for page 1.")
        return []
    max_page = get_max_page(first_page_json)

    all_urls = []
    # Extract URLs from the first page using the new 'content' field
    html_str = first_page_json.get("content", "")
    urls = parse_listing_html(html_str)
    all_urls.extend(urls)

    for p in range(2, max_page + 1):
        json_data = get_listing_data(page=p)
        if json_data:
            html_str = json_data.get("content", "")
            urls = parse_listing_html(html_str)
            all_urls.extend(urls)
        time.sleep(1)

    # Remove duplicates while preserving order
    unique_urls = []
    seen = set()
    for url in all_urls:
        if url not in seen:
            unique_urls.append(url)
            seen.add(url)

    return unique_urls

# ----------------------------
# Part 2: Reward Detail Scraper
# ----------------------------
def parse_reward_fields(container: BeautifulSoup) -> dict:
    """
    Parses the reward page container (id="reward-fields") by finding all heading widgets
    (h2 elements with class "elementor-heading-title") and then collecting the next element’s content.
    For fields like "images" and "posters" it extracts all link hrefs; for others it returns the text.
    Returns a dictionary where keys are lowercased field names (without trailing colon) and values are the extracted content.
    """
    fields = {}
    # Find all h2 elements within the container
    headings = container.find_all("h2", class_="elementor-heading-title")

    for heading in headings:
        key = heading.get_text(strip=True).rstrip(":").lower()

        # Skip certain headings that are not data fields
        if key in ["submit a tip", "do your part", "about"]:
            continue

        # Find the next element in document order (that is a tag and is not a heading)
        next_elem = heading.find_next(lambda tag: tag.name and tag.name != "h2")
        if not next_elem:
            fields[key] = None
            continue

        if key in ["images", "posters"]:
            # For gallery fields extract all <a> tag hrefs
            links = [a["href"] for a in next_elem.find_all("a", href=True)]
            fields[key] = links if links else []
        elif key in ["associated location(s)", "citizenship", "aliases/alternative name spellings"]:
            # For these fields, extract text and split by common delimiters
            text_val = next_elem.get_text(separator=" ", strip=True)
            if text_val:
                # Split by common delimiters and clean up
                items = re.split(r'[,;]\s*', text_val)
                fields[key] = [item.strip() for item in items if item.strip()]
            else:
                fields[key] = []
        else:
            # For simple text fields
            text_val = next_elem.get_text(separator=" ", strip=True)
            fields[key] = text_val if text_val else None

    return fields

def parse_reward_page(url: str) -> dict:
    """
    Fetches the reward URL and extracts details from the container with id "reward-fields".
    Also extracts the main title and reward amount from the page.
    Returns a dictionary containing the URL and the extracted fields.
    """
    try:
        # Use session with better error handling
        session = requests.Session()
        session.verify = False
        response = session.get(url, timeout=30)
    except Exception as e:
        return {"url": url, "error": str(e)}

    if not response.ok:
        return {"url": url, "error": f"HTTP {response.status_code}"}

    soup = BeautifulSoup(response.content, 'lxml')

    # Initialize fields with URL
    fields = {"url": url}

    # Extract main title and reward amount from the page
    try:
        # Look for the main title (usually the first large heading)
        title_elem = soup.find("h1") or soup.find("h2", class_="elementor-heading-title")
        if title_elem:
            title_text = title_elem.get_text(strip=True)
            if title_text and not title_text.startswith("Up to"):
                fields["title"] = title_text

        # Look for reward amount (usually starts with "Up to")
        reward_headings = soup.find_all("h2", class_="elementor-heading-title")
        for heading in reward_headings:
            text = heading.get_text(strip=True)
            if text.startswith("Up to"):
                fields["reward_amount"] = text
                break
    except Exception:
        pass

    # Extract fields from reward-fields container
    container = soup.find(id="reward-fields")
    if container:
        reward_fields = parse_reward_fields(container)
        fields.update(reward_fields)

    return fields

def fetch_reward_details(urls: list) -> pd.DataFrame:
    """
    Given a list of reward URLs, fetches each reward detail page and
    returns a DataFrame with the extracted details.
    """
    records = []
    for url in urls:
        details = parse_reward_page(url)
        records.append(details)
        time.sleep(1)  # be polite
    df = pd.DataFrame(records)
    return df

def clean_reward_amount(amount_str) -> str:
    """
    Extracts numeric value from reward amount string.
    Example: "Up to $10 million dollars" -> "10000000"
    """
    if not amount_str:
        return ""

    # Convert to string if it's not already
    if not isinstance(amount_str, str):
        amount_str = str(amount_str)

    # Handle NaN values
    if amount_str.lower() in ['nan', 'none', 'null']:
        return ""

    # Remove common words and extract numbers
    amount_str = amount_str.lower().replace("up to", "").replace("dollars", "").replace("$", "").strip()

    # Handle million/billion multipliers
    multiplier = 1
    if "million" in amount_str:
        multiplier = 1000000
        amount_str = amount_str.replace("million", "").strip()
    elif "billion" in amount_str:
        multiplier = 1000000000
        amount_str = amount_str.replace("billion", "").strip()

    # Extract numeric value
    import re
    numbers = re.findall(r'\d+\.?\d*', amount_str)
    if numbers:
        try:
            return str(int(float(numbers[0]) * multiplier))
        except:
            return ""
    return ""

def transform_to_standard_format(df: pd.DataFrame) -> pd.DataFrame:
    """
    Transforms the scraped data to the standardized format used by other scrapers.
    """
    if df.empty:
        return pd.DataFrame(columns=generalized_cols)

    # Initialize standardized columns
    df_std = pd.DataFrame()

    # Basic mappings
    df_std['FullName'] = df.get('title', '')
    df_std['EntityType'] = 'Individual'  # Most RFJ rewards are for individuals
    df_std['FirstName'] = None
    df_std['LastName'] = None

    # Handle DOB - not typically available in RFJ data
    df_std['DOB'] = [[] for _ in range(len(df))]

    # Handle addresses from associated locations
    def extract_addresses(row):
        addresses = row.get('associated location(s)', [])
        if isinstance(addresses, list):
            return addresses
        elif addresses:
            return [addresses]
        else:
            return []

    df_std['Address'] = df.apply(extract_addresses, axis=1)

    # Handle countries from citizenship and locations
    def extract_countries(row):
        countries = []
        # From citizenship
        citizenship = row.get('citizenship', [])
        if isinstance(citizenship, list):
            for c in citizenship:
                if c and str(c).lower() not in ['nan', 'none', 'null']:
                    countries.append(str(c))
        elif citizenship and str(citizenship).lower() not in ['nan', 'none', 'null']:
            countries.append(str(citizenship))

        # From locations
        locations = row.get('associated location(s)', [])
        if isinstance(locations, list):
            for loc in locations:
                if loc and str(loc).lower() not in ['nan', 'none', 'null']:
                    country_codes = get_country_code(str(loc))
                    countries.extend(country_codes)
        elif locations and str(locations).lower() not in ['nan', 'none', 'null']:
            country_codes = get_country_code(str(locations))
            countries.extend(country_codes)

        # Remove duplicates and filter out any remaining NaN/None values
        unique_countries = []
        for country in countries:
            if country and str(country).lower() not in ['nan', 'none', 'null'] and country not in unique_countries:
                unique_countries.append(country)

        return unique_countries

    df_std['Countries'] = df.apply(extract_countries, axis=1)

    # Handle aliases
    def extract_aliases(row):
        aliases = row.get('aliases/alternative name spellings', [])
        if isinstance(aliases, list):
            return aliases
        elif aliases:
            return [aliases]
        else:
            return []

    df_std['Alias'] = df.apply(extract_aliases, axis=1)

    # Standard fields
    df_std['EffectiveOrConvictionDate'] = None
    df_std['ReasonOrSanctionType'] = 'Terrorism Reward'
    df_std['SourceName'] = 'Rewards for Justice'
    df_std['SourceCode'] = 'RFJ'
    df_std['Url'] = df.get('url', '')
    df_std['LatestUpdate'] = get_current_time()
    df_std['AdditionDate'] = None
    df_std['DeletionDate'] = None
    df_std['NewAoD'] = False
    df_std['RecordStatus'] = ''

    # Additional fields for extra data
    additional_data = []
    for _, row in df.iterrows():
        extra_fields = {}

        # Add reward amount with proper naming
        if 'reward_amount' in row and row['reward_amount'] and str(row['reward_amount']).lower() not in ['nan', 'none', 'null']:
            extra_fields['rewardAmount'] = str(row['reward_amount'])
            numeric_amount = clean_reward_amount(row['reward_amount'])
            if numeric_amount:
                extra_fields['rewardAmountNumeric'] = numeric_amount

        # Add gender/sex with proper naming
        if 'sex' in row and row['sex'] and str(row['sex']).lower() not in ['nan', 'none', 'null']:
            extra_fields['gender'] = str(row['sex'])

        # Add images and posters with proper handling
        if 'images' in row and row['images'] and str(row['images']).lower() not in ['nan', 'none', 'null']:
            if isinstance(row['images'], list):
                extra_fields['images'] = row['images']
            else:
                extra_fields['images'] = [str(row['images'])]

        if 'posters' in row and row['posters'] and str(row['posters']).lower() not in ['nan', 'none', 'null']:
            if isinstance(row['posters'], list):
                extra_fields['posters'] = row['posters']
            else:
                extra_fields['posters'] = [str(row['posters'])]

        # Add other personal details with proper field naming
        field_mapping = {
            'date of birth': 'dateOfBirth',
            'place of birth': 'placeOfBirth',
            'nationality': 'nationality',
            'hair color': 'hairColor',
            'eye color': 'eyeColor',
            'height': 'height',
            'weight': 'weight',
            'distinguishing features': 'distinguishingFeatures',
            'languages spoken': 'languagesSpoken',
            'build': 'build',
            'complexion': 'complexion',
            'occupation': 'occupation',
            'associated organizations': 'associatedOrganizations',
            'known locales': 'knownLocales',
            'national identification number(s) and country': 'nationalIdNumbers',
            'passport number(s) and country': 'passportNumbers',
            'miscellaneous identifications': 'miscellaneousIds',
            'clan': 'clan'
        }

        # Add mapped fields with proper null handling
        for original_key, mapped_key in field_mapping.items():
            if original_key in row and row[original_key] and str(row[original_key]).lower() not in ['nan', 'none', 'null']:
                extra_fields[mapped_key] = str(row[original_key])

        # Skip empty keys and add only non-empty additional fields
        for key, value in row.items():
            if (key not in ['title', 'url', 'associated location(s)', 'citizenship',
                          'aliases/alternative name spellings', 'reward_amount', 'sex',
                          'images', 'posters'] + list(field_mapping.keys()) and
                value and str(value).lower() not in ['nan', 'none', 'null', ''] and
                key.strip() != ''):
                # Clean up the key name
                clean_key = key.replace('(s)', '').replace(' ', '_').replace('-', '_').lower()
                if clean_key:
                    extra_fields[clean_key] = str(value)

        additional_data.append(extra_fields)

    df_std['AdditionalFields'] = additional_data

    # Create unique IDs
    df_std['SocureId'] = create_unique_ids(df_std)

    return df_std

# ----------------------------
# Main orchestrator: Listing then Detail scraper
# ----------------------------
def rfj_rewards(run_id, is_manual_update):
    """
    Main function for RFJ Rewards scraper following the standard pattern.
    Orchestrates the full scraping process and handles data persistence.
    """
    try:
        source_note = 'rfj_rewards'
        statsd.increment('scraper.started', tags=['socure:'+source_note])

        # Setup directories
        os.umask(18)  # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        # Get current time for this run
        LatestUpdate = get_current_time()

        # Check if new update is needed
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        logger.info(f"previous_update_date: {previous_update_date}, new_update: {new_update}")
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])

            # Step 1: Get all reward URLs
            reward_urls = fetch_listing_urls()
            if not reward_urls:
                logger.error("No reward URLs were collected.")
                return

            # For testing purposes, you can limit URLs by uncommenting the line below
            # reward_urls = reward_urls[:5]  # Uncomment to limit for testing

            # Step 2: Fetch details for each URL
            df_raw = fetch_reward_details(reward_urls)

            # Step 3: Transform to standard format
            df = transform_to_standard_format(df_raw)

            if df.empty:
                logger.warning("No data after transformation")
                return

            # Ensure all required columns are present
            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            # Save master file
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')

            # Check for additions/deletions if previous data exists
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            # Final cleanup
            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            # Setup senzing directory
            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            # Update for senzing
            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)

            # Record success metrics
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])

            # Save metadata
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)

            # Update last run timestamp
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()

            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} total records : {noOfRecords}")

            return df

        else:
            logger.info("No new update available.")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.completed', tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of {source_note} for runId {run_id}: {' '.join(tb_lines)}")

        # Ensure senzing directory exists before writing error file
        senzing_dir = data_Path + "senzing/"
        os.makedirs(senzing_dir, exist_ok=True)

        with open(senzing_dir + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    import sys
    source_note = 'rfj_rewards'
    logger.info(f"Starting the scrapping for {source_note}")
    globals()[sys.argv[1]](sys.argv[2], sys.argv[3])
