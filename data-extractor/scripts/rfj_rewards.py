import os
import time
import warnings
import logging
import sys
import json
import requests
import pandas as pd
import numpy as np
import re
import traceback
from bs4 import BeautifulSoup
from datetime import datetime
from datadog import initialize, statsd

warnings.simplefilter('ignore')

# Datadog settings (adjust if necessary)
host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()

# Import utils functions
from utils import (
    try_date_parse, get_today, get_current_time, additional_fields,
    create_unique_ids, is_new_update_available, check_for_addition_and_deletion_v2,
    update_for_senzing, data_Path, generalized_cols, get_country_code, get_proxy,
    http_with_retries
)

# ----------------------------
# Part 1: Listing Page Scraper
# ----------------------------
def get_listing_data(page: int) -> dict:
    """
    Sends a POST request to retrieve the listing JSON for a given page number.
    Returns the JSON dictionary if successful, else None.
    Uses http_with_retries for better reliability.
    """
    base_url = (
        "https://rewardsforjustice.net/index/?jsf=jet-engine%3Arewards-grid&"
        "tax=crime-category%3A1072&nocache=1744046245"
    )
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "origin": "https://rewardsforjustice.net",
        "referer": "https://rewardsforjustice.net/index/?jsf=jet-engine:rewards-grid&tax=crime-category:1072",
        "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ),
        "x-requested-with": "XMLHttpRequest"
    }
    payload = (
        "action=jet_engine_ajax&handler=get_listing&"
        "page_settings%5Bpost_id%5D=22076&"
        "page_settings%5Bqueried_id%5D=22076%7CWP_Post&"
        "page_settings%5Belement_id%5D=ddd7ae9&"
        "page_settings%5Bpage%5D={page}&"
        "listing_type=elementor&"
        "isEditMode=false&"
        "addedPostCSS%5B%5D=22078"
    ).format(page=page)

    try:
        # Use POST request with retries
        session = requests.Session()
        session.verify = False
        response = session.post(base_url, headers=headers, data=payload, timeout=20)
        logger.info("HTTP response status (page %d): %s", page, response.status_code)
    except requests.RequestException as e:
        logger.error("Error during request on page %d: %s", page, e)
        return None

    content_type = response.headers.get('Content-Type', '')
    if response.ok and "application/json" in content_type:
        try:
            json_data = response.json()
            return json_data
        except Exception as e:
            logger.error("Error parsing JSON response on page %d: %s", page, e)
            return None
    else:
        logger.error("Response is not JSON on page %d. Status: %s, Content: %s",
                     page, response.status_code, response.text[:200])
        return None

def get_max_page(json_data: dict) -> int:
    """
    Extracts the maximum page number from the JSON.
    It first checks inside json_data['data'] for filters_data->props->rewards-grid->max_num_pages.
    If not found, it falls back to parsing the HTML's data-pages attribute.
    Returns 1 if no valid number is found.
    """
    data_obj = json_data.get("data", {})
    filters_data = data_obj.get("filters_data")
    if filters_data:
        props = filters_data.get("props")
        if props:
            rewards_grid = props.get("rewards-grid")
            if rewards_grid:
                try:
                    max_pages = int(rewards_grid.get("max_num_pages", 0))
                    if max_pages > 0:
                        logger.info("Extracted max_pages from filters_data: %d", max_pages)
                        return max_pages
                except Exception as e:
                    logger.error("Error converting max_num_pages: %s", e)
    # Fallback: look for the data-pages attribute in the HTML
    html_str = data_obj.get("html", "")
    soup = BeautifulSoup(html_str, 'lxml')
    grid_div = soup.find("div", class_="jet-listing-grid")
    if grid_div and grid_div.has_attr("data-pages"):
        try:
            max_page = int(grid_div["data-pages"])
            logger.info("Extracted max_pages from HTML data-pages attribute: %d", max_page)
            return max_page
        except ValueError:
            logger.error("Failed to convert data-pages attribute to integer.")
            return 1
    logger.debug("No valid max page found; defaulting to 1")
    return 1

def parse_listing_html(html_str: str) -> list:
    """
    Extracts and returns a list of reward page URLs from the listing HTML.
    """
    soup = BeautifulSoup(html_str, 'lxml')
    post_items = soup.find_all("div", class_="jet-listing-grid__item")
    urls = []
    for item in post_items:
        overlay_link = item.find("a", class_="jet-engine-listing-overlay-link")
        post_url = overlay_link["href"] if overlay_link and overlay_link.has_attr("href") else None
        if post_url:
            urls.append(post_url)
    return urls

def fetch_listing_urls() -> list:
    """
    Retrieves all reward URLs from the listing pages.
    """
    first_page_json = get_listing_data(page=1)
    if first_page_json is None:
        logger.error("Failed to retrieve listing JSON for page 1.")
        return []
    max_page = get_max_page(first_page_json)
    logger.info("Total pages found: %d", max_page)

    all_urls = []
    data_obj = first_page_json.get("data", {})
    html_str = data_obj.get("html", "")
    urls = parse_listing_html(html_str)
    logger.info("Page 1: Found %d URLs.", len(urls))
    all_urls.extend(urls)

    for p in range(2, max_page + 1):
        logger.info("Scraping page %d for URLs...", p)
        json_data = get_listing_data(page=p)
        if json_data:
            html_str = json_data.get("data", {}).get("html", "")
            urls = parse_listing_html(html_str)
            logger.info("Page %d: Found %d URLs.", p, len(urls))
            all_urls.extend(urls)
        else:
            logger.error("Skipping page %d due to missing JSON data.", p)
        time.sleep(1)

    # Remove duplicates while preserving order
    unique_urls = []
    seen = set()
    for url in all_urls:
        if url not in seen:
            unique_urls.append(url)
            seen.add(url)

    logger.info("Total URLs collected: %d", len(all_urls))
    logger.info("Unique URLs after deduplication: %d", len(unique_urls))

    # Log the unique URLs for debugging
    if len(unique_urls) <= 20:  # Only log if reasonable number
        logger.info("Unique URLs found: %s", unique_urls)
    else:
        logger.info("First 10 unique URLs: %s", unique_urls[:10])
        logger.info("Last 10 unique URLs: %s", unique_urls[-10:])

    return unique_urls

# ----------------------------
# Part 2: Reward Detail Scraper
# ----------------------------
def parse_reward_fields(container: BeautifulSoup) -> dict:
    """
    Parses the reward page container (id="reward-fields") by finding all heading widgets
    (h2 elements with class "elementor-heading-title") and then collecting the next element’s content.
    For fields like "images" and "posters" it extracts all link hrefs; for others it returns the text.
    Returns a dictionary where keys are lowercased field names (without trailing colon) and values are the extracted content.
    """
    fields = {}
    # Find all h2 elements within the container
    headings = container.find_all("h2", class_="elementor-heading-title")

    for heading in headings:
        key = heading.get_text(strip=True).rstrip(":").lower()

        # Skip certain headings that are not data fields
        if key in ["submit a tip", "do your part", "about"]:
            continue

        # Find the next element in document order (that is a tag and is not a heading)
        next_elem = heading.find_next(lambda tag: tag.name and tag.name != "h2")
        if not next_elem:
            fields[key] = None
            continue

        if key in ["images", "posters"]:
            # For gallery fields extract all <a> tag hrefs
            links = [a["href"] for a in next_elem.find_all("a", href=True)]
            fields[key] = links if links else []
        elif key in ["associated location(s)", "citizenship", "aliases/alternative name spellings"]:
            # For these fields, extract text and split by common delimiters
            text_val = next_elem.get_text(separator=" ", strip=True)
            if text_val:
                # Split by common delimiters and clean up
                items = re.split(r'[,;]\s*', text_val)
                fields[key] = [item.strip() for item in items if item.strip()]
            else:
                fields[key] = []
        else:
            # For simple text fields
            text_val = next_elem.get_text(separator=" ", strip=True)
            fields[key] = text_val if text_val else None

    return fields

def parse_reward_page(url: str) -> dict:
    """
    Fetches the reward URL and extracts details from the container with id "reward-fields".
    Also extracts the main title and reward amount from the page.
    Returns a dictionary containing the URL and the extracted fields.
    """
    logger.debug("Fetching reward page: %s", url)
    try:
        # Use session with better error handling
        session = requests.Session()
        session.verify = False
        response = session.get(url, timeout=30)
    except Exception as e:
        logger.error("Error fetching URL %s: %s", url, e)
        return {"url": url, "error": str(e)}

    if not response.ok:
        logger.error("Failed to fetch URL %s, status code %s", url, response.status_code)
        return {"url": url, "error": f"HTTP {response.status_code}"}

    soup = BeautifulSoup(response.content, 'lxml')

    # Initialize fields with URL
    fields = {"url": url}

    # Extract main title and reward amount from the page
    try:
        # Look for the main title (usually the first large heading)
        title_elem = soup.find("h1") or soup.find("h2", class_="elementor-heading-title")
        if title_elem:
            title_text = title_elem.get_text(strip=True)
            if title_text and not title_text.startswith("Up to"):
                fields["title"] = title_text

        # Look for reward amount (usually starts with "Up to")
        reward_headings = soup.find_all("h2", class_="elementor-heading-title")
        for heading in reward_headings:
            text = heading.get_text(strip=True)
            if text.startswith("Up to"):
                fields["reward_amount"] = text
                break
    except Exception as e:
        logger.warning("Error extracting title/reward for %s: %s", url, e)

    # Extract fields from reward-fields container
    container = soup.find(id="reward-fields")
    if container:
        reward_fields = parse_reward_fields(container)
        fields.update(reward_fields)
    else:
        logger.warning("Reward fields container not found on page: %s", url)

    return fields

def fetch_reward_details(urls: list) -> pd.DataFrame:
    """
    Given a list of reward URLs, fetches each reward detail page and
    returns a DataFrame with the extracted details.
    """
    records = []
    for i, url in enumerate(urls):
        logger.info("Processing URL %d/%d: %s", i+1, len(urls), url)
        details = parse_reward_page(url)
        records.append(details)
        time.sleep(1)  # be polite
    df = pd.DataFrame(records)
    return df

def clean_reward_amount(amount_str: str) -> str:
    """
    Extracts numeric value from reward amount string.
    Example: "Up to $10 million dollars" -> "10000000"
    """
    if not amount_str:
        return ""

    # Remove common words and extract numbers
    amount_str = amount_str.lower().replace("up to", "").replace("dollars", "").replace("$", "").strip()

    # Handle million/billion multipliers
    multiplier = 1
    if "million" in amount_str:
        multiplier = 1000000
        amount_str = amount_str.replace("million", "").strip()
    elif "billion" in amount_str:
        multiplier = 1000000000
        amount_str = amount_str.replace("billion", "").strip()

    # Extract numeric value
    import re
    numbers = re.findall(r'\d+\.?\d*', amount_str)
    if numbers:
        try:
            return str(int(float(numbers[0]) * multiplier))
        except:
            return ""
    return ""

def transform_to_standard_format(df: pd.DataFrame) -> pd.DataFrame:
    """
    Transforms the scraped data to the standardized format used by other scrapers.
    """
    if df.empty:
        return pd.DataFrame(columns=generalized_cols)

    # Initialize standardized columns
    df_std = pd.DataFrame()

    # Basic mappings
    df_std['FullName'] = df.get('title', '')
    df_std['EntityType'] = 'Individual'  # Most RFJ rewards are for individuals
    df_std['FirstName'] = None
    df_std['LastName'] = None

    # Handle DOB - not typically available in RFJ data
    df_std['DOB'] = [[] for _ in range(len(df))]

    # Handle addresses from associated locations
    df_std['Address'] = df.get('associated location(s)', []).apply(
        lambda x: x if isinstance(x, list) else ([x] if x else [])
    )

    # Handle countries from citizenship and locations
    def extract_countries(row):
        countries = []
        # From citizenship
        citizenship = row.get('citizenship', [])
        if isinstance(citizenship, list):
            countries.extend(citizenship)
        elif citizenship:
            countries.append(citizenship)

        # From locations
        locations = row.get('associated location(s)', [])
        if isinstance(locations, list):
            for loc in locations:
                country_codes = get_country_code(str(loc))
                countries.extend(country_codes)
        elif locations:
            country_codes = get_country_code(str(locations))
            countries.extend(country_codes)

        return list(set(countries))  # Remove duplicates

    df_std['Countries'] = df.apply(extract_countries, axis=1)

    # Handle aliases
    df_std['Alias'] = df.get('aliases/alternative name spellings', []).apply(
        lambda x: x if isinstance(x, list) else ([x] if x else [])
    )

    # Standard fields
    df_std['EffectiveOrConvictionDate'] = None
    df_std['ReasonOrSanctionType'] = 'Terrorism Reward'
    df_std['SourceName'] = 'Rewards for Justice'
    df_std['SourceCode'] = 'RFJ'
    df_std['Url'] = df.get('url', '')
    df_std['LatestUpdate'] = get_current_time()
    df_std['AdditionDate'] = None
    df_std['DeletionDate'] = None
    df_std['NewAoD'] = False
    df_std['RecordStatus'] = ''

    # Additional fields for extra data
    additional_data = []
    for _, row in df.iterrows():
        extra_fields = {}

        # Add reward amount
        if 'reward_amount' in row and row['reward_amount']:
            extra_fields['reward_amount'] = row['reward_amount']
            extra_fields['reward_amount_numeric'] = clean_reward_amount(row['reward_amount'])

        # Add gender/sex
        if 'sex' in row and row['sex']:
            extra_fields['sex'] = row['sex']

        # Add images and posters
        if 'images' in row and row['images']:
            extra_fields['images'] = row['images']
        if 'posters' in row and row['posters']:
            extra_fields['posters'] = row['posters']

        # Add any other fields not mapped to standard columns
        for key, value in row.items():
            if key not in ['title', 'url', 'associated location(s)', 'citizenship',
                          'aliases/alternative name spellings', 'reward_amount', 'sex',
                          'images', 'posters'] and value:
                extra_fields[key] = value

        additional_data.append(extra_fields)

    df_std['AdditionalFields'] = additional_data

    # Create unique IDs
    df_std['SocureId'] = create_unique_ids(df_std)

    return df_std

# ----------------------------
# Main orchestrator: Listing then Detail scraper
# ----------------------------
def rfj_rewards(run_id, is_manual_update):
    """
    Main function for RFJ Rewards scraper following the standard pattern.
    Orchestrates the full scraping process and handles data persistence.
    """
    try:
        source_note = 'rfj_rewards'
        statsd.increment('scraper.started', tags=['socure:'+source_note])

        # Setup directories
        os.umask(18)  # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        # Get current time for this run
        LatestUpdate = get_current_time()

        # Check if new update is needed
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            logger.info(f"Starting RFJ Rewards scraping for runId {run_id}")

            # Step 1: Get all reward URLs
            reward_urls = fetch_listing_urls()
            if not reward_urls:
                logger.error("No reward URLs were collected.")
                return

            # For testing purposes, you can limit URLs by uncommenting the line below
            # reward_urls = reward_urls[:5]  # Uncomment to limit for testing

            logger.info(f"Found {len(reward_urls)} reward URLs to process")

            # Step 2: Fetch details for each URL
            df_raw = fetch_reward_details(reward_urls)
            logger.info(f"Scraped details for {len(df_raw)} rewards")

            # Step 3: Transform to standard format
            df = transform_to_standard_format(df_raw)
            logger.info(f"Transformed to standard format: {len(df)} records")

            if df.empty:
                logger.warning("No data after transformation")
                return

            # Ensure all required columns are present
            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            # Save master file
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')

            # Check for additions/deletions if previous data exists
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            # Final cleanup
            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            # Setup senzing directory
            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            # Update for senzing
            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)

            # Record success metrics
            logger.info(f"Success for {source_note} for runId {run_id}")
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])

            # Save metadata
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)

            # Update last run timestamp
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()

            logger.info(f"Number of records {source_note} for runId {run_id}: {noOfRecords}")
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords, tags=['socure:'+source_note])

            return df

        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords, tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of {source_note} for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    # Usage: python rfj_rewards.py rfj_rewards test123 false
    globals()[sys.argv[1]](sys.argv[2], sys.argv[3])
