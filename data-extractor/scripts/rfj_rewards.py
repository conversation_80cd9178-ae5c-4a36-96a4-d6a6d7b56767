import os
import time
import warnings
import logging
import sys
import json
import requests
import pandas as pd
from bs4 import BeautifulSoup
from datetime import datetime
from datadog import initialize, statsd

warnings.simplefilter('ignore')

# Datadog settings (adjust if necessary)
host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,  # Change to INFO when finished debugging
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()

# ----------------------------
# Part 1: Listing Page Scraper
# ----------------------------
def get_listing_data(page: int) -> dict:
    """
    Sends a POST request to retrieve the listing JSON for a given page number.
    Returns the JSON dictionary if successful, else None.
    """
    base_url = (
        "https://rewardsforjustice.net/index/?jsf=jet-engine%3Arewards-grid&"
        "tax=crime-category%3A1072&nocache=1744046245"
    )
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "en-GB,en-US;q=0.9,en;q=0.8",
        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
        "origin": "https://rewardsforjustice.net",
        "referer": "https://rewardsforjustice.net/index/?jsf=jet-engine:rewards-grid&tax=crime-category:1072",
        "sec-ch-ua": '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"macOS"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": (
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
            "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        ),
        "x-requested-with": "XMLHttpRequest"
    }
    payload = (
        "action=jet_engine_ajax&handler=get_listing&"
        "page_settings%5Bpost_id%5D=22076&"
        "page_settings%5Bqueried_id%5D=22076%7CWP_Post&"
        "page_settings%5Belement_id%5D=ddd7ae9&"
        "page_settings%5Bpage%5D={page}&"
        "listing_type=elementor&"
        "isEditMode=false&"
        "addedPostCSS%5B%5D=22078"
    ).format(page=page)

    try:
        response = requests.post(base_url, headers=headers, data=payload, timeout=20)
        logger.info("HTTP response status (page %d): %s", page, response.status_code)
    except requests.RequestException as e:
        logger.error("Error during request on page %d: %s", page, e)
        return None

    content_type = response.headers.get('Content-Type', '')
    if response.ok and "application/json" in content_type:
        try:
            json_data = response.json()
        except Exception as e:
            logger.error("Error parsing JSON response on page %d: %s", page, e)
            return None
    else:
        logger.error("Response is not JSON on page %d. Status: %s, Content: %s",
                     page, response.status_code, response.text)
        return None
    return json_data

def get_max_page(json_data: dict) -> int:
    """
    Extracts the maximum page number from the JSON.
    It first checks inside json_data['data'] for filters_data->props->rewards-grid->max_num_pages.
    If not found, it falls back to parsing the HTML's data-pages attribute.
    Returns 1 if no valid number is found.
    """
    data_obj = json_data.get("data", {})
    filters_data = data_obj.get("filters_data")
    if filters_data:
        props = filters_data.get("props")
        if props:
            rewards_grid = props.get("rewards-grid")
            if rewards_grid:
                try:
                    max_pages = int(rewards_grid.get("max_num_pages", 0))
                    if max_pages > 0:
                        logger.info("Extracted max_pages from filters_data: %d", max_pages)
                        return max_pages
                except Exception as e:
                    logger.error("Error converting max_num_pages: %s", e)
    # Fallback: look for the data-pages attribute in the HTML
    html_str = data_obj.get("html", "")
    soup = BeautifulSoup(html_str, 'lxml')
    grid_div = soup.find("div", class_="jet-listing-grid")
    if grid_div and grid_div.has_attr("data-pages"):
        try:
            max_page = int(grid_div["data-pages"])
            logger.info("Extracted max_pages from HTML data-pages attribute: %d", max_page)
            return max_page
        except ValueError:
            logger.error("Failed to convert data-pages attribute to integer.")
            return 1
    logger.debug("No valid max page found; defaulting to 1")
    return 1

def parse_listing_html(html_str: str) -> list:
    """
    Extracts and returns a list of reward page URLs from the listing HTML.
    """
    soup = BeautifulSoup(html_str, 'lxml')
    post_items = soup.find_all("div", class_="jet-listing-grid__item")
    urls = []
    for item in post_items:
        overlay_link = item.find("a", class_="jet-engine-listing-overlay-link")
        post_url = overlay_link["href"] if overlay_link and overlay_link.has_attr("href") else None
        if post_url:
            urls.append(post_url)
    return urls

def fetch_listing_urls() -> list:
    """
    Retrieves all reward URLs from the listing pages.
    """
    first_page_json = get_listing_data(page=1)
    if first_page_json is None:
        logger.error("Failed to retrieve listing JSON for page 1.")
        return []
    max_page = get_max_page(first_page_json)
    logger.info("Total pages found: %d", max_page)

    all_urls = []
    data_obj = first_page_json.get("data", {})
    html_str = data_obj.get("html", "")
    urls = parse_listing_html(html_str)
    logger.info("Page 1: Found %d URLs.", len(urls))
    all_urls.extend(urls)

    for p in range(2, max_page + 1):
        logger.info("Scraping page %d for URLs...", p)
        json_data = get_listing_data(page=p)
        if json_data:
            html_str = json_data.get("data", {}).get("html", "")
            urls = parse_listing_html(html_str)
            logger.info("Page %d: Found %d URLs.", p, len(urls))
            all_urls.extend(urls)
        else:
            logger.error("Skipping page %d due to missing JSON data.", p)
        time.sleep(1)

    logger.info("Total URLs collected: %d", len(all_urls))
    return all_urls

# ----------------------------
# Part 2: Reward Detail Scraper
# ----------------------------
def parse_reward_fields(container: BeautifulSoup) -> dict:
    """
    Parses the reward page container (id="reward-fields") by finding all heading widgets
    (h2 elements with class "elementor-heading-title") and then collecting the next element’s content.
    For fields like "images" and "posters" it extracts all link hrefs; for others it returns the text.
    Returns a dictionary where keys are lowercased field names (without trailing colon) and values are the extracted content.
    """
    fields = {}
    # Find all h2 elements within the container
    headings = container.find_all("h2", class_="elementor-heading-title")
    for heading in headings:
        key = heading.get_text(strip=True).rstrip(":").lower()
        # Find the next element in document order (that is a tag and is not a heading)
        next_elem = heading.find_next(lambda tag: tag.name and tag.name != "h2")
        if not next_elem:
            fields[key] = None
            continue
        if key in ["images", "posters"]:
            # For gallery fields extract all <a> tag hrefs
            links = [a["href"] for a in next_elem.find_all("a", href=True)]
            fields[key] = links if links else None
        else:
            text_val = next_elem.get_text(separator=" ", strip=True)
            fields[key] = text_val if text_val else None
    return fields

def parse_reward_page(url: str) -> dict:
    """
    Fetches the reward URL and extracts details from the container with id "reward-fields".
    Returns a dictionary containing the URL and the extracted fields.
    """
    logger.info("Fetching reward page: %s", url)
    try:
        response = requests.get(url, timeout=20)
    except Exception as e:
        logger.error("Error fetching URL %s: %s", url, e)
        return {"url": url}

    if not response.ok:
        logger.error("Failed to fetch URL %s, status code %s", url, response.status_code)
        return {"url": url}

    soup = BeautifulSoup(response.content, 'lxml')
    container = soup.find(id="reward-fields")
    if not container:
        logger.error("Reward fields container not found on page: %s", url)
        return {"url": url}

    fields = parse_reward_fields(container)
    fields["url"] = url
    return fields

def fetch_reward_details(urls: list) -> pd.DataFrame:
    """
    Given a list of reward URLs, fetches each reward detail page and
    returns a DataFrame with the extracted details.
    """
    records = []
    for url in urls:
        details = parse_reward_page(url)
        records.append(details)
        time.sleep(1)  # be polite
    df = pd.DataFrame(records)
    return df

# ----------------------------
# Main orchestrator: Listing then Detail scraper
# ----------------------------
def rfj_rewards(run_id, is_manual_update):
    """
    Orchestrates the full scraping process.
    First gathers all reward URLs from the listing pages,
    then fetches each reward detail page and stores the extracted data in a DataFrame.
    Finally prints the DataFrame as JSON.
    """
    reward_urls = fetch_listing_urls()
    if not reward_urls:
        logger.error("No reward URLs were collected.")
        return

    df = fetch_reward_details(reward_urls)
    print(df)
    # logger.info("Total reward detail records scraped: %d", df.shape[0])
    print(df.to_json(orient="records", indent=4))

if __name__ == '__main__':
    # Usage: python rfj_rewards.py rfj_rewards test123 false
    globals()[sys.argv[1]](sys.argv[2], sys.argv[3])
