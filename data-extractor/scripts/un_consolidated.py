from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')
import re
from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import logging
import traceback
import requests
import urllib3
from requests.adapters import HTTPAdapter
from urllib3.util.ssl_ import create_urllib3_context

class UnsafeLegacyRenegotiationAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        context = create_urllib3_context()
        context.options |= 0x4  # OP_LEGACY_SERVER_CONNECT
        context.check_hostname = False  # Disable hostname checking
        context.verify_mode = ssl.CERT_NONE  # Disable certificate verification
        kwargs['ssl_context'] = context
        return super().init_poolmanager(*args, **kwargs)

    def proxy_manager_for(self, *args, **kwargs):
        context = create_urllib3_context()
        context.options |= 0x4  # OP_LEGACY_SERVER_CONNECT
        context.check_hostname = False  # Disable hostname checking
        context.verify_mode = ssl.CERT_NONE  # Disable certificate verification
        kwargs['ssl_context'] = context
        return super().proxy_manager_for(*args, **kwargs)

# Create a session
session = requests.Session()

# Mount our adapter to the session
adapter = UnsafeLegacyRenegotiationAdapter()
session.mount("https://", adapter)

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
os.environ['OPENSSL_CONF'] = '/etc/ssl/openssl.cnf'
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion_v2, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, get_country_code, get_proxy

proxies = get_proxy()
session.proxies = proxies
#-----------------------------------------------------------------------------------------------
# Module for UN Consolidated
generalized_cols = ['SocureId', 'EntityType', 'FirstName', 'LastName', 'FullName',
                    'DOB', 'Address', 'Countries', 'Alias', 'EffectiveOrConvictionDate',
                    'ReasonOrSanctionType', 'SourceName', 'SourceCode', 'Url',
                    'LatestUpdate', 'AdditionDate', 'DeletionDate', 'NewAoD', 'AdditionalFields', 'RecordStatus']

def UN_address_process(x):
    adds = ""
    a_keys = ['STREET', 'CITY', 'STATE_PROVINCE', 'COUNTRY', 'ZIP_CODE']
    try:
        if not isinstance(x, list):
            x = [x]
        for a in x:
            # print(a)
            try:
                c = ""
                for k in a_keys:
                    if k in a.keys():
                        c = c + a[k].replace(",", "") + ", "
                c = c.replace(", ,", ",").replace(", ,", ",").strip(", ")
                adds = adds + c.replace(";", "") + "; "
            except:
                continue
    except:
        adds = ""
    return adds.strip("; ")

def UN_alias_process(x):
    # 8	[{'QUALITY': 'Good', 'ALIAS_NAME': 'Kim Hak Song'}, {'QUALITY': None, 'ALIAS_NAME': None}]
    aliases = ""
    try:
        if not isinstance(x, list):
            x = [x]
        for a in x:
            try:
                name = a['ALIAS_NAME']
                name = re.sub(r'\s*\n\s*', ' ', name)
                aliases = aliases + name.replace(",", "") + ", "
            except:
                continue
    except:
        aliases = ""
    return aliases.strip(', ')


def UN_DOB_process(x):
    # 8	[{'TYPE_OF_DATE': 'EXACT', 'DATE': '1968-03-26'}, {'TYPE_OF_DATE': 'EXACT', 'DATE': '1970-10-15'}]
    dob = []
    try:
        if not isinstance(x, list):
            x = [x]
        for a in x:
            try:
                d = a['DATE']
                dob.append(try_date_parse(d))
            except:
                try:
                    d = a['YEAR']
                    dob.append(try_date_parse(d))
                except:
                    continue
    except:
        dob = []
    return sorted(list(set(dob)))

def clean_text(text):
    cleaned_text = re.sub(r'\s+', ' ', text).strip()
    return cleaned_text

def get_function_value(d):
    try:
        return [clean_text(d['VALUE'])]
    except:
        return []

def get_title_value(d):
    try:
        if isinstance(d['VALUE'], list):
            return d['VALUE']
        else:
            return [d['VALUE']]
    except:
        return []

def get_passport_info(d):
    try:
        passports = []
        if isinstance(d, dict):
            d = [d]
        for val in d:
            info = ""
            if val['TYPE_OF_DOCUMENT']=='Passport':
                passport_number = val['NUMBER'] if 'NUMBER' in val.keys() else "None"
                issue_country = val['ISSUING_COUNTRY'] if 'ISSUING_COUNTRY' in val.keys() else "None"
                note = val['NOTE'] if 'NOTE' in val.keys() else "None"

                info = "Passport: " + passport_number + ", issue_country: " + issue_country + ", note: " + note
            if len(info)>0:
                passports.append(info)
        return passports
    except:
        return []

def get_amendedOn(d):
    try:
        if isinstance(d['VALUE'], list):
            return [d['VALUE'][-1]]
        else:
            return [d['VALUE']]
    except:
        return []

def get_placeOfbirth(d):
    try:
        if isinstance(d, list):
            return sorted(list(set([val['COUNTRY'] for val in d])))
        else:
            return [d['COUNTRY']]
    except:
        return []

def get_nationality(d):
    try:
        if isinstance(d['VALUE'], list):
            return d['VALUE']
        else:
            return [d['VALUE']]
    except:
        return []

def un_combined_countries(d):
    return sorted(list(set(d)))


def un_consolidated_indi(df, LatestUpdate, run_id):
    global data_Path
    global generalized_cols
    source_url = 'https://www.un.org/securitycouncil/'
    source_note = 'un_consolidated'

    c_mapper = {"FIRST_NAME": "FirstName", "FOURTH_NAME": "LastName",  "INDIVIDUAL_ALIAS": "Alias",
                "INDIVIDUAL_DATE_OF_BIRTH": "DOB", "INDIVIDUAL_ADDRESS": "Address",
                "type": "EntityType"}

    SourceCode = 'UNC'
    SourceName = 'UN Consolidated'

    fName_raw = data_Path + source_note + "/" + source_note + '_indi_original'+ '_'+ LatestUpdate + '.csv'
    # df.to_csv(fName_raw, index=False)

    df['COMMENTS1'] = df['COMMENTS1'].fillna("")
    df = df.rename(columns={'COMMENTS1': 'otherInformation'})
    df['otherInformation'] = df['otherInformation'].apply(lambda x: [x])

    df['offense'] ='Sanction'
    df['offense'] = df['offense'].apply(lambda x: [x])

    df = df.rename(columns={'DESIGNATION': 'function'})
    df['function'] = df['function'].apply(get_function_value)

    df['issuingAuthority'] = "United Nations"
    df['issuingAuthority'] = df['issuingAuthority'].apply(lambda x: [x])

    df['EffectiveOrConvictionDate'] = df['LISTED_ON']

    df = df.rename(columns={'LISTED_ON': 'designationDate'})
    df['designationDate'] = df['designationDate'].fillna("")
    df['designationDate'] = df['designationDate'].apply(lambda x: [x])

    df = df.rename(columns={'REFERENCE_NUMBER':'unListingId'})
    df['unListingId'] = df['unListingId'].fillna("")
    df['unListingId'] = df['unListingId'].apply(lambda x: [x])

    df['passport'] = df['INDIVIDUAL_DOCUMENT'].apply(get_passport_info)

    df = df.rename(columns={'UN_LIST_TYPE':'program'})
    df['program'] = df['program'].fillna("")
    df['program'] = df['program'].apply(lambda x: [x])

    df = df.rename(columns={'GENDER':'gender'})
    df['gender'] = df['gender'].fillna("").str.lower()
    df['gender'] = df['gender'].apply(lambda x: [x])

    df = df.rename(columns={'TITLE':'title'})
    df['title'] = df['title'].apply(get_title_value)

    df = df.rename(columns={'LAST_DAY_UPDATED':'amendedOn'})
    df['amendedOn'] = df['amendedOn'].apply(get_amendedOn)

    df['placeOfBirth'] = df['INDIVIDUAL_PLACE_OF_BIRTH'].apply(get_placeOfbirth)

    df = df.rename(columns={'NATIONALITY':'nationality'})
    df['nationality'] = df['nationality'].apply(get_nationality)


    AdditionalFields = additional_fields(df, c_mapper)
    df = df.rename(columns=c_mapper)
    df['AdditionalFields'] = AdditionalFields

    fName = data_Path + source_note + "/" + source_note + '_indi_'+ LatestUpdate + '.csv'

    df['FirstName'] = df['FirstName'].fillna("")
    df['LastName'] = df['LastName'].fillna("")
    df['SECOND_NAME'] = df['SECOND_NAME'].fillna("")
    df['THIRD_NAME'] = df['THIRD_NAME'].fillna("")
    df['EntityType'] = df['EntityType']
    df['FullName'] = df['FirstName'] + " " + df['SECOND_NAME']+ " " + df['THIRD_NAME']+ " " + df['LastName']
    df['FullName'] = df['FullName'].str.strip()
    df['FullName'] = df['FullName'].str.title()
    df['DOB'] = df['DOB'].apply(UN_DOB_process)
    df['Address'] = df['Address'].apply(UN_address_process)
    df['Countries'] = df['Address'].apply(get_country_code)
    df['country_from_pob'] = [get_country_code(str(val[0])) if len(val) > 0 else [] for val in df['placeOfBirth'].values]
    df['country_from_nationality'] = [get_country_code(val[0]) if len(val)>0 else [] for val in df['nationality'].values ]
    df['Countries'] = df['Countries'] + df['country_from_pob'] + df['country_from_nationality']
    df['Countries'] = df['Countries'].apply(un_combined_countries)
    df['Address'] = df['Address'].str.split('; ')
    df['Alias'] = df['Alias'].apply(UN_alias_process)
    df['NAME_ORIGINAL_SCRIPT'] = df['NAME_ORIGINAL_SCRIPT'].fillna("")
    df['NAME_ORIGINAL_SCRIPT'] = df['NAME_ORIGINAL_SCRIPT'].str.replace(",", "")
    df['Alias'] = df['Alias'] + ", " + df['NAME_ORIGINAL_SCRIPT']
    df['Alias'] = df['Alias'].str.split(', ')
    df['ReasonOrSanctionType'] = None
    df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(try_date_parse)
    df['SourceName'] = SourceName
    df['SourceCode'] = SourceCode
    df['Url'] = source_url
    df['LatestUpdate'] = LatestUpdate
    df['AdditionDate'] = None
    df['DeletionDate'] = None
    df['NewAoD'] = False
    df['RecordStatus'] = ''

    SocureId = create_unique_ids(df)

    df['SocureId'] = SocureId

    df = df[generalized_cols]
    df = df.drop_duplicates(subset="SocureId", keep="first")
    # df.to_csv(fName, index=False)

    logger.info(f"Success for {source_note} individual for runId {run_id}")
    return df


# ['COMMENTS1', 'DATAID', 'ENTITY_ADDRESS', 'ENTITY_ALIAS', 'FIRST_NAME',
#        'LAST_DAY_UPDATED', 'LISTED_ON', 'LIST_TYPE', 'REFERENCE_NUMBER',
#        'SORT_KEY', 'SORT_KEY_LAST_MOD', 'UN_LIST_TYPE', 'VERSIONNUM',
#        'NAME_ORIGINAL_SCRIPT', 'SUBMITTED_ON', 'type']
def un_consolidated_entity(df, LatestUpdate, run_id):
    """
    """
    global data_Path
    source_url = 'https://www.un.org/securitycouncil/'
    source_note = 'un_consolidated'

    c_mapper = {"FIRST_NAME": "FullName", "ENTITY_ALIAS": "Alias", "ENTITY_ADDRESS": "Address", "type": "EntityType"}

    SourceCode = 'UNC'
    SourceName = 'UN Consolidated'

    fName_raw = data_Path + source_note + "/" + source_note + '_entity_original'+ '_'+ LatestUpdate + '.csv'
    # df.to_csv(fName_raw, index=False)

    df['COMMENTS1'] = df['COMMENTS1'].fillna("")
    df = df.rename(columns={'COMMENTS1': 'otherInformation'})
    df['otherInformation'] = df['otherInformation'].apply(lambda x: [x])

    df['offense'] ='Sanction'
    df['offense'] = df['offense'].apply(lambda x: [x])

    df['issuingAuthority'] = "United Nations"
    df['issuingAuthority'] = df['issuingAuthority'].apply(lambda x: [x])

    df['EffectiveOrConvictionDate'] = df['LISTED_ON']

    df = df.rename(columns={'LISTED_ON': 'designationDate'})
    df['designationDate'] = df['designationDate'].fillna("")
    df['designationDate'] = df['designationDate'].apply(lambda x: [x])

    df = df.rename(columns={'REFERENCE_NUMBER':'unListingId'})
    df['unListingId'] = df['unListingId'].fillna("")
    df['unListingId'] = df['unListingId'].apply(lambda x: [x])

    df = df.rename(columns={'UN_LIST_TYPE':'program'})
    df['program'] = df['program'].fillna("")
    df['program'] = df['program'].apply(lambda x: [x])

    df = df.rename(columns={'LAST_DAY_UPDATED':'amendedOn'})
    df['amendedOn'] = df['amendedOn'].apply(get_amendedOn)

    AdditionalFields = additional_fields(df, c_mapper)
    df = df.rename(columns=c_mapper)
    df['AdditionalFields'] = AdditionalFields

    fName = data_Path + source_note + "/" + source_note + '_entity_'+ LatestUpdate + '.csv'

    df['FirstName'] = ""
    df['LastName'] = ""
    df['EntityType'] = "Organization"
    df['FullName'] = df['FullName'].str.strip()
    df['FullName'] = df['FullName'].str.title()
    df['DOB'] = [[] for _ in range(len(df))]
    df['Address'] = df['Address'].apply(UN_address_process)
    df['Countries'] = df['Address'].apply(get_country_code)
    df['Address'] = df['Address'].str.split('; ')
    df['Alias'] = df['Alias'].apply(UN_alias_process)
    df['NAME_ORIGINAL_SCRIPT'] = df['NAME_ORIGINAL_SCRIPT'].fillna("")
    df['NAME_ORIGINAL_SCRIPT'] = df['NAME_ORIGINAL_SCRIPT'].str.replace(",", "")
    df['Alias'] = df['Alias'] + ", " + df['NAME_ORIGINAL_SCRIPT']
    df['Alias'] = df['Alias'].str.split(', ')
    df['ReasonOrSanctionType'] = None
    df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(try_date_parse)
    df['SourceName'] = SourceName
    df['SourceCode'] = SourceCode
    df['Url'] = source_url
    df['LatestUpdate'] = LatestUpdate
    df['AdditionDate'] = None
    df['DeletionDate'] = None
    df['NewAoD'] = False
    df['RecordStatus'] = ''

    SocureId = create_unique_ids(df)
    df['SocureId'] = SocureId

    df = df[generalized_cols]
    df = df.drop_duplicates(subset="SocureId", keep="first")
    # df.to_csv(fName, index=False)

    logger.info(f"Success for {source_note} entity for runId {run_id}")
    return df

def un_consolidated(run_id, is_manual_update):
    try:
        global data_Path
        # URL = "https://www.un.org/securitycouncil/sites/www.un.org.securitycouncil/files/consolidated.xml"
        URL = "https://scsanctions.un.org/resources/xml/en/consolidated.xml"
        source_note = 'un_consolidated'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        headers = { "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"}



        # r = requests.get(URL, headers=headers, allow_redirects=True, verify=False)
        r = session.get(URL, headers=headers, verify=False)

        logger.info(f"http response status of {source_note} {r.status_code} for runId {run_id}")


        btree = BeautifulSoup(r.content, "xml")
        INDIVIDUALS = btree.select('INDIVIDUALS > INDIVIDUAL')

        dfs = []
        for i in INDIVIDUALS:
            obj = xmltodict.parse(str(i))
            dfs.append(pd.DataFrame(obj).transpose())
        dfi = pd.concat(dfs, ignore_index=True)
        dfi = dfi.reset_index(drop = True)
        dfi['type'] = 'Individual'

        ENTITIES = btree.select('ENTITIES > ENTITY')
        dfs = []
        for i in ENTITIES:
            obj = xmltodict.parse(str(i))
            dfs.append(pd.DataFrame(obj).transpose())
        dfe = pd.concat(dfs, ignore_index=True)
        dfe = dfe.reset_index(drop = True)
        dfe['type'] = 'Entity'

        LatestUpdate = try_date_parse_hour_min(btree.select('CONSOLIDATED_LIST')[0].attrs['dateGenerated'])

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"

        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)

        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                df = pd.concat([un_consolidated_indi(dfi, LatestUpdate, run_id), un_consolidated_entity(dfe, LatestUpdate, run_id)], ignore_index=True)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id}")
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} number of records {noOfRecords}")

            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])


    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of un_consolidated for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])
        handle_exception(source_note, previous_update_date, logName, err)

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])