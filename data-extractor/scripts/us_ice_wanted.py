from typing import Optional, Dict, List
import os
import warnings
import traceback
import sys
warnings.simplefilter('ignore')
import logging
from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import re
import time
import logging
import traceback


host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import get_country_code, get_current_time, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_proxy


import time
def fetch_url(url):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br"
    }
    session = requests.Session()  # Use session for keeping state across requests
    proxies = get_proxy()
    session.proxies = proxies
    try:
        response = session.get(url, headers=headers, verify=False, proxies=proxies)
        if response.status_code == 403:
            logger.info("Access Denied, trying again with delay...")
            time.sleep(10)
            response = session.get(url, headers=headers, verify=False, proxies=proxies)
        return response
    except requests.exceptions.RequestException as e:
        logging.error(f" failure log  {traceback.format_exc()}")

def us_ice_wanted(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL = "https://www.ice.gov/most-wanted"

        source_url = URL

        source_note = 'us_ice_wanted'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        c_mapper = {"NAME": "FullName", "ALIAS": "Alias", "PLACE OF BIRTH": "placeOfBirth", "LAST KNOWN LOCATION": "Address"}

        SourceCode = 'USICEW'
        SourceName = 'United States Immigration and Customs Wanted'
        source_url = URL

        d = get_current_time()
        LatestUpdate = d

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'


            df = pd.DataFrame()
            if is_manual_update == 'true':
                df = pd.read_parquet(local_source_path + source_note + '/source_data.parquet')
            else:

                response = fetch_url(URL)
                logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
                soup = BeautifulSoup(response.text, 'lxml')

                s = soup.find_all("span", {"class": "field-content"})
                entities = [tag.find('a')['href'] for tag in s]

                details = []
                for en in entities:
                    time.sleep(2)
                    headers = {
                        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
                        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                        "Accept-Language": "en-US,en;q=0.9",
                        "Accept-Encoding": "gzip, deflate, br"
                    }
                    proxies = get_proxy()
                    en_response = requests.request('GET', en, headers=headers, verify=False, proxies=proxies)
                    # logger.info(f"en_response text of scraping us_ice_wanted {en_response.text} for runId  {run_id}")
                    # logger.info(f"en_response status code of scraping us_ice_wanted {en_response.status_code} for runId {run_id}")
                    en_soup = BeautifulSoup(en_response.text, 'lxml')
                    content = en_soup.find_all("div", {"id": "block-system-main-block"})
                    detail = {}

                    try:
                        c = content[0].find("div", {"class": "field--name-field-most-wanted-name"})
                        name = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        break

                    try:
                        c = content[0].find("div", {"class": "field--name-field-most-wanted-alias"})
                        alias = c.find("div", {"class": "field__item"}).text.strip()
                        alias = alias.replace(","," ").replace("; ", "  ").replace('“', '').replace('”', '').replace(' aka ', ' ').replace("/", "  ").replace('"', '').replace(" or ", "  ").split("  ")
                    except:
                        alias = []

                    try:
                        c = content[0].find("div", {"class": "field--name-field-gender"})
                        gender = c.find("div", {"class": "field__item"}).text.strip().lower()
                    except:
                        gender = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-place-of-birth"})
                        pob = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        pob = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-age"})
                        age = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        age = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-last-known-location"})
                        lkc = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        lkc = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-most-wanted-occupation"})
                        occupation = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        occupation = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-height"})
                        height = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        height = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-weight"})
                        weight = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        weight = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-skin-tone"})
                        skin_tone = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        skin_tone = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-eyes"})
                        eyes = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        eyes = ""

                    try:
                        c = content[0].find("div", {"class": "field--name-field-hair"})
                        hair = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        hair = ""


                    try:
                        c = content[0].find("div", {"class": "field--name-field-scars-marks"})
                        scar = c.find("div", {"class": "field__item"}).text.strip()
                    except:
                        scar = ""

                    try:
                        c = content[0].find("div", {"class": "field--type-text-with-summary"})
                        summary = c.find("p").text.strip()
                    except:
                        summary = ""

                    detail = {"NAME": name, "ALIAS": alias, "gender": gender,
                              "PLACE OF BIRTH": pob, "LAST KNOWN LOCATION": lkc, "occupation": occupation,
                              "height": height, "weight": weight, "skinTone": skin_tone, "eyeColor": eyes,
                              "hair": hair,   "distinguishingFeatures": scar, "summary": summary}

                    details.append(detail)
                data_dict = {}
                for key in list(details[0].keys()):
                    data_dict[key] = []

                for data in details:
                    for key in data.keys():
                        data_dict[key].append(data[key])

                df = pd.DataFrame()
                for key in data_dict.keys():
                    df[key] = data_dict[key]
                df['Url'] = entities[:len(df)]
            # print('fName_raw: ', fName_raw)
            # df.to_csv(fName_raw, index=False)

                df = df.rename(columns=c_mapper)
                df = df.fillna('')
                df['FirstName'] = ""
                df['LastName'] = ""
                df['EntityType'] = "Individual"
                df['FullName'] = df['FullName']
                df['FullName'] = df['FullName'].str.strip()
                df['FullName'] = df['FullName'].str.title()
                df['DOB'] = [[] for _ in range(len(df))]
                df["placeOfBirth"] = df["placeOfBirth"].str.title()
                df['Address'] = df['Address'].str.title()
                df['Address'] = df['Address'].apply(lambda x: [x] if len(x)>0 else [])
                df['Countries'] = df['placeOfBirth'].apply(get_country_code)
                df['ReasonOrSanctionType'] = None
                df['EffectiveOrConvictionDate'] = None
                df['offense'] = 'Warning'
                df['offense'] = df['offense'].apply(lambda x: [x])
                df['SourceName'] = SourceName
                df['SourceCode'] = SourceCode
                if not is_manual_update:
                    df['Url'] = entities[:len(df)]
                df['LatestUpdate'] = LatestUpdate
                df['AdditionDate'] = None
                df['DeletionDate'] = None
                df = df.dropna(subset=['FullName'])
                df['NewAoD'] = False
                df['RecordStatus'] = ''

                additional_fields_mapper = {"NAME": "FullName", "ALIAS": "Alias", "LAST KNOWN LOCATION": "Address"}
                AdditionalFields = additional_fields(df, additional_fields_mapper)
                df['AdditionalFields'] = AdditionalFields

                SocureId = create_unique_ids(df)
                df['SocureId'] = SocureId

                df = df.drop_duplicates(subset="SocureId", keep="first")
                df = df[generalized_cols]
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} total records : {noOfRecords}")
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of us_ice_wanted for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])


if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])