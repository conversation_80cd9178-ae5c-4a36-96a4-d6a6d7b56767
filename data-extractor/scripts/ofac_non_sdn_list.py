import os
import warnings
import sys
warnings.simplefilter('ignore')

from utils import  data_Path, get_current_time, try_date_parse_hour_min, additional_fields, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, handle_exception, update_for_senzing, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_proxy

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from datetime import date
from datetime import datetime
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import ast
import ssl
from ast import literal_eval
import re
import string


from lxml import etree
import xmltodict

import pycountry
import spacy
from functools import lru_cache
# Load the spaCy English model
# nlp = spacy.load("en_core_web_lg")
import logging
import traceback
from datadog import initialize, statsd



host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
ssl._create_default_https_context = ssl._create_unverified_context
global current_run_id

generalized_cols = ['SocureId', 'EntityType', 'FirstName', 'LastName', 'FullName',
                    'DOB', 'Address', 'Countries', 'Alias', 'EffectiveOrConvictionDate',
                    'ReasonOrSanctionType', 'SourceName', 'SourceCode', 'Url',
                    'LatestUpdate', 'AdditionDate', 'DeletionDate', 'NewAoD', 'AdditionalFields', 'RecordStatus']


def get_val(d, k):
    try:
        return d[k]
    except:
        return ""

def extract_akas(d):
    try:
        val = d['aka']
        akas = []
        try:
            for v in val:
                aka = {}
                for k in v.keys():
                    if k!='uid':
                        aka[k] = v[k]
                akas.append(aka)
        except:
            aka = {}
            for k in val.keys():
                if k!='uid':
                    aka[k] = val[k]
            akas.append(aka)
        return akas
    except:
        return []

def get_name(d):
    name = get_val(d, 'firstName').strip() + " " + get_val(d, 'lastName').strip()
    return name.strip()

def get_akas(d):
    try:
        return [{'name': get_name(v), 'type': get_val(v, 'type'), 'category': get_val(v, 'category')} for v in d]
    except:
        return []

def extract_dobs(d):
    try:
        val = d['dateOfBirthItem']
        try:
            return [v['dateOfBirth'] for v in val]
        except:
            return [val['dateOfBirth']]
    except:
        return []

def extract_dobs_place(d):
    try:
        val = d['placeOfBirthItem']
        try:
            return [v['placeOfBirth'] for v in val]
        except:
            return [val['placeOfBirth']]
    except:
        return []

def extract_addresses(d):
    try:
        val = d['address']
        adds = []
        try:
            for v in val:
                add = {}
                for k in v.keys():
                    if k!='uid':
                        add[k] = v[k]
                adds.append(add)
        except:
            add = {}
            for k in val.keys():
                if k!='uid':
                    add[k] = val[k]
            adds.append(add)
        return adds
    except:
        return []

def extract_ids(d):
    try:
        val = d['id']
        ids = []
        try:
            for v in val:
                idx = {}
                for k in v.keys():
                    if k!='uid':
                        idx[k] = v[k]
                ids.append(idx)
        except:
            idx = {}
            for k in val.keys():
                if k!='uid':
                    idx[k] = val[k]
            ids.append(idx)
        return ids
    except:
        return []


def OFAC_alias_process(x):
    aliases = []
    try:
        for a in x:
            try:
                aka = a['name'].replace(",", "")
                if len(aka)>0:
                    aliases.append(string.capwords(aka))
            except:
                continue
        return aliases
    except:
        return aliases

# def OFAC_alias_process(x):
#     aliases = ""
#     try:
#         for a in x:
#             try:
#                 aliases = aliases + a['name'].replace(",", "") + ", "
#             except:
#                 continue
#     except:
#         aliases = ""
#     return aliases.strip(', ')

# def OFAC_address_process(x):
#     adds = ""
#     try:
#         for a in x:
#             try:
#                 a = ", ".join(a[key] for key in a.keys()).replace(", ,", ",").replace(", ,", ",").strip(", ")
#                 adds = adds + a.replace(";", "") + "; "
#             except:
#                 continue
#     except:
#         adds = ""
#     return adds.strip("; ")

def OFAC_address_process(x):
    adds = []
    try:
        for a in x:
            try:
                a = ", ".join(a[key] for key in a.keys()).replace(", ,", ",").replace(", ,", ",").strip(", ")
                adds.append(a)
            except:
                continue

        return adds
    except:
        return adds


def try_dob_parse_OFAC(st, dayF = False, yearF = False):
    dobs = []
    try:
        # st = st.replace("00/", "")
        # xs = st.split(';')
        xs = st
        for x in xs:
            x = x.strip()
            # print(x)
            try:
                if len(x)==4:
                    dobs.append(parse(x, dayfirst=False, yearfirst=False).date().strftime("%Y-00-00"))
                else:
                    dobs.append(parse(x, dayfirst=dayF, yearfirst=yearF).date().strftime("%Y-%m-%d"))
            except:
                try:
                    for d in x.split('to'):
                        d = d.strip()
                        if len(d)==4:
                            dobs.append(parse(d, dayfirst=False, yearfirst=False).date().strftime("%Y-00-00"))
                        else:
                            dobs.append(parse(d, dayfirst=dayF, yearfirst=yearF).date().strftime("%Y-%m-%d"))
                except:
                    # print(x)
                    pass
    except:
        # print(xs)
        pass
    return dobs


@lru_cache(maxsize=None)
def get_country_code(country_name):
    try:
        country_name = country_name.lower().replace("'", "")
        if 'burma' in country_name:
            country_name = 'myanmar'
        elif 'gaza' in country_name or 'palestinian' in country_name:
            country_name = 'palestine'

        return pycountry.countries.search_fuzzy(country_name)[0].alpha_2
    except LookupError:
        return None

def OFAC_get_countries(data):
    country_codes = set()
    for item in data:
        if 'country' in item:
            country_codes.add(item['country'])
    return sorted(list(country_codes))

def OFAC_get_country_codes(data):
    country_codes = set()
    for item in data:
        if 'country' in item:
            code = get_country_code(item['country'])
            if code:
                country_codes.add(code)
    return sorted(list(country_codes))

def get_nationality(d):
    try:
        val = d['nationality']
        ids = []
        try:
            for v in val:
                for k in v.keys():
                    if k=='country':
                        ids[k].append(v[k])
        except:
            ids.append(val['country'])
        return sorted(list(set(ids)))
    except:
        return []

def get_passport_info(d):
    try:
        passports = []
        for val in d:
            info = ""
            if val['idType']=='Passport':
                passport_number = val['idNumber'] if 'idNumber' in val.keys() else ""
                issue_date = val['issueDate'] if 'issueDate' in val.keys() else ""
                expiration_date = val['expirationDate'] if 'expirationDate' in val.keys() else ""
                issue_country = val['idCountry'] if 'idCountry' in val.keys() else ""

                info = "Passport: " + passport_number + ", issuing date " + str(try_date_parse(issue_date)) + ", expiration date " + str(try_date_parse(expiration_date)) + ", issue_country: " + issue_country
            if len(info)>0:
                passports.append(info)
        return passports
    except:
        return []

def get_additional_sanctions_info(d):
    try:
        additionalSanctionsInformation = []
        for val in d:
            info = ""
            if val['idType']=='Secondary sanctions risk:':
                additionalSanctionsInformation.append(val['idType'] + " - "+ str(val['idNumber']))
        return additionalSanctionsInformation
    except:
        return []

def get_gender(d):
    try:
        gender = []
        for val in d:
            if val['idType']=='Gender':
                gender.append(val['idNumber'].lower())
        return gender
    except:
        return []

def extract_programs(d):
    try:
        return d['program']
    except:
        return []

def try_date_parse(x, dayF = False, yearF = False):
    try:
        x = x.replace("00/", "")
        if len(x)==4:
            return parse(x, dayfirst=False, yearfirst=False).date().strftime("%Y-00-00")
        else:
            return parse(x, dayfirst=dayF, yearfirst=yearF).date().strftime("%Y-%m-%d")
    except:
        return None

def try_date_parse_hour_min(x, dayF = False, yearF = False):
    try:
        x = x.replace("00/", "")
        if len(x)==4:
            return parse(x, dayfirst=False, yearfirst=False).strftime("%Y-00-00-00-00")
        else:
            return parse(x, dayfirst=dayF, yearfirst=yearF).strftime("%Y-%m-%dT%H:%M:%S")
    except:
        return None

def try_dob_parse(x, dayF = False, yearF = False):
    try:
        x = x.replace("00/", "")
        if len(x)==4:
            return [parse(x, dayfirst=False, yearfirst=False).date().strftime("%Y-00-00")]
        else:
            return [parse(x, dayfirst=dayF, yearfirst=yearF).date().strftime("%Y-%m-%d")]
    except:
        return []


def extract_dates(string):
    # This regex captures YYYY-MM-DD, YYYY-MM, and YYYY formats.
    date_pattern = r'\b\d{4}(?:[-/]\d{1,2}(?:[-/]\d{1,2})?)?\b'
    return re.findall(date_pattern, string)

def try_date_parse_OFAC(st, dayF = False, yearF = False):
    try:
        st = st.replace("00/", "")
        dobs = []
        try:
            xs = st.split(';')
            for x in xs:
                x = extract_dates(x)[0]
                if len(x)==4:
                    dobs.append(parse(x, dayfirst=False, yearfirst=False).date().strftime("%Y-00-00"))
                else:
                    dobs.append(parse(x, dayfirst=dayF, yearfirst=yearF).date().strftime("%Y-%m-%d"))
            return dobs
        except:
            xs = x.split('to')
            for x in xs:
                if len(x)==4:
                    dobs.append(parse(x, dayfirst=False, yearfirst=False).date().strftime("%Y-00-00"))
                else:
                    dobs.append(parse(x, dayfirst=dayF, yearfirst=yearF).date().strftime("%Y-%m-%d"))
            return dobs
    except:
        return []

def extract_dates_dob(string):
    # Regex pattern to capture various date formats
    date_pattern = r'\b(?:\d{1,2}[-/]\d{1,2}[-/]\d{4}|\d{4}[-/]\d{1,2}(?:[-/]\d{1,2})?|\d{1,2} (?:jan(?:uary)?|feb(?:ruary)?|mar(?:ch)?|apr(?:il)?|may|jun(?:e)?|jul(?:y)?|aug(?:ust)?|sep(?:tember)?|oct(?:ober)?|nov(?:ember)?|dec(?:ember)?) \d{4}|(?:jan(?:uary)?|feb(?:ruary)?|mar(?:ch)?|apr(?:il)?|may|jun(?:e)?|jul(?:y)?|aug(?:ust)?|sep(?:tember)?|oct(?:ober)?|nov(?:ember)?|dec(?:ember)?) \d{4}|\d{4})\b'

    return re.findall(date_pattern, string, re.IGNORECASE)

def try_dob_parse_OFAC_to(st):
    try:
        xs = st.split('to')
        start = parse(extract_dates_dob(xs[0])[-1], dayfirst=False, yearfirst=False).date()
        end = parse(extract_dates_dob(xs[1])[0], dayfirst=False, yearfirst=False).date()
        dobs = [str(dob) for dob in range(start.year+1, end.year)]
        return dobs
    except Exception as e:
        # print(e)
        return []

def try_dob_parse_OFAC_single(st, dayF = False, yearF = False):
    try:
        st = st.replace("00/", "")
        dobs = []
        xs = extract_dates_dob(st)
        xs_to = try_dob_parse_OFAC_to(st)
        xs = sorted(list(set(xs+xs_to)))
        # print(xs)
        for x in xs:
            if len(x)==4:
                d = parse(x, dayfirst=False, yearfirst=False).date()
            else:
                d = parse(x, dayfirst=dayF, yearfirst=yearF).date()
            year = str(d.year) if x.find(str(d.year))!=-1 else "0000"
            if year!="0000":
                x = x.replace(year, "0000")

            # date_obj = datetime.strptime(d, "%Y-%m-%d")
            fm = d.strftime("%B")
            sm = d.strftime("%b")

            month = str(d.month)
            month = "0"+month if len(month)==1 else month
            month = month if x.find(fm)!=-1 or x.find(month)!=-1 or x.find(sm)!=-1 else "00"
            day = str(d.day)
            day = "0"+day if len(day)==1 else day
            day = day if x.find(str(d.day))!=-1 else "00"

            d = year + "-" + month + "-" + day
            if d.find("0000")==-1:
                dobs.append(d)
        return sorted(list(set(dobs)))
    except Exception as e:
        # print(e)
        return []
def try_dob_parse_OFAC(st, dayF = False, yearF = False):
    dobs = []
    try:
        xs = st
        for x in xs:
            x = x.strip()
            try:
                dobs = dobs+try_dob_parse_OFAC_single(x)
            except Exception as e:
                print(e)
                pass
    except Exception as e:
        # print(xs)
        print(e)
        pass
    return sorted(list(set(dobs)))

def try_date_parse_gbi(x):
    try:
        return parse(x, dayfirst=False, yearfirst=False).date().strftime("%Y-00-00")
    except:
        return None

def get_today():
    today = date.today()
    return today.strftime("%Y-%m-%d")

def get_current_time():
    now = datetime.now()
    current_time = now.strftime("%Y-%m-%dT%H:%M:%S")
    return current_time

def additional_fields(df, c_mapper):
    d_cols = ['SocureId', 'EntityType', 'FirstName', 'LastName', 'FullName',
              'DOB', 'Address', 'Countries', 'Alias', 'EffectiveOrConvictionDate',
              'ReasonOrSanctionType', 'SourceName', 'SourceCode', 'Url',
              'LatestUpdate', 'AdditionDate', 'DeletionDate', 'NewAoD', 'RecordStatus']
    df1 = df.copy()
    df1 = df1.fillna("")
    df1 = pd.DataFrame(df1).astype(str)
    AdditionalFields = []
    for i in range(len(df1)):
        d = {}
        for col in df.columns:
            if col not in c_mapper.keys() and col not in c_mapper.values() and col not in d_cols:
                if df1.iloc[i][col]=="":
                    continue
                if df1.iloc[i][col]=="[]":
                    continue
                else:
                    d[col] = df1.iloc[i][col]
        AdditionalFields.append(d)

    return AdditionalFields

def form_SocureId(df, SourceCode):
    SocureId = []
    for i in range(len(df)):
        fn = SourceCode + '_' + df.iloc[i]['FullName'] + str(i)
        hash_object = hashlib.sha1(fn.encode())
        hex_dig = hash_object.hexdigest()
        SocureId.append(hex_dig)

    return SocureId

def return_sha_has(val):
    hash_object = hashlib.sha1(val.encode())
    hex_dig = hash_object.hexdigest()
    return hex_dig

def create_unique_ids(df, cols = ['EntityType', 'FullName', 'DOB', 'Address', 'Countries', 'Alias', 'SourceCode',
                                  'ReasonOrSanctionType', 'EffectiveOrConvictionDate', 'AdditionalFields']):
    df1 = df.copy()
    df = df.drop(columns=['LatestUpdate'])
    for col in cols:
        df1[col] = df1[col].astype(str)
    df1 = df1[cols]
    df1 = df1.fillna('')
    return df1[cols].sum(axis=1).map(return_sha_has)



def check_for_addition_and_deletion(df, LatestUpdate, oldFile):
    previous = pd.read_parquet(oldFile)
    previous['NewAoD'] = False
    if 'Countries' not in previous.columns:
        previous['Countries'] = [[] for _ in range(len(previous))]
    df = df[generalized_cols]
    merged = pd.concat([previous, df], ignore_index=True)
    merged = merged.drop_duplicates(subset=['SocureId'], keep='first')
    merged['matched_prev'] = merged['SocureId'].isin(previous['SocureId'])
    merged['matched_df'] = merged['SocureId'].isin(df['SocureId'])

    merged = merged.set_index(['SocureId'])
    logger.info(f"previous records count {previous.shape[0]} for runId {current_run_id}")
    logger.info(f"current records count {df.shape[0]} for runId {current_run_id}")

    if merged.shape[0]!=df.shape[0]:
        new_idx = merged[merged['matched_prev']==False].index
        deleted_idx = merged[merged['matched_df']==False].index
    else:
        new_idx = []
        deleted_idx = []

    merged['RecordStatus'] = ''
    for idx in new_idx:
        merged.at[idx, 'AdditionDate'] = LatestUpdate
        merged.at[idx, 'RecordStatus'] = 'Add'
        merged.at[idx, 'NewAoD'] = True

    for indx in deleted_idx:
        if pd.isnull(merged.at[indx, 'DeletionDate']) or merged.at[indx, 'DeletionDate'] == None or merged.at[indx, 'DeletionDate'] == "":
            merged.at[indx, 'DeletionDate'] = LatestUpdate
            merged.at[indx, 'RecordStatus'] = 'Delete'
            merged.at[indx, 'NewAoD'] = True

    if len(merged[merged["NewAoD"]==True])>0 or previous.shape[0]==0:
        logger.info(f"New updates are available! for runId {current_run_id}")
    else:
        logger.info(f"No new Update for runId {current_run_id} !")

    merged['LatestUpdate'] = LatestUpdate
    return merged.reset_index()



def lit_eval(x):
    try:
        return ast.literal_eval(x)
    except:
        return None



def ofac_non_sdn_list(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols
        current_run_id = run_id
        URL="https://sanctionssearch.ofac.treas.gov/"
        source_url = URL

        source_note = 'ofac_non_sdn_list'

        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        SourceCode = 'OFAC'
        SourceName = 'OFAC Non SDN List'

        c_mapper = {}

        d = get_current_time()
        LatestUpdate = d
        try:
            proxies = get_proxy()
            response = requests.request('GET', URL, headers={}, data={}, verify=False, proxies=proxies)
            logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
            soup = BeautifulSoup(response.text, 'lxml')
            results_div = soup.find_all('div',{'id':'framePage'})
            b = [i.select('span') for i in results_div][0]
            sdn_last_update = [i.text for i in b if i.get('id') if i.get('id') == 'ctl00_lblSDNPubDate'][0]
            non_sdn_last_update = [i.text for i in b if i.get('id') if i.get('id') == 'ctl00_lblFSEPubDate'][0]
            last_update = {sdn_last_update[:sdn_last_update.index(':')].strip():sdn_last_update[sdn_last_update.index(':')+1:].strip(),non_sdn_last_update[:non_sdn_last_update.index(':')].strip():non_sdn_last_update[non_sdn_last_update.index(':')+1:].strip()}
            t = last_update['Non-SDN List last updated on'].split(' ')
            last_update_date = str(t[0]+'_'+t[1]+t[2]).replace(r'/','-')
            LatestUpdate = try_date_parse_hour_min(last_update['Non-SDN List last updated on'])

        except:
            LatestUpdate = d

        # print(last_update_date, LatestUpdate)
        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"

        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)


        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                xml_url = 'https://www.treasury.gov/ofac/downloads/consolidated/consolidated.xml'
                proxies = get_proxy()
                response = requests.request('GET', xml_url, headers={}, data={}, verify=False, proxies=proxies)
                logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
                soup = BeautifulSoup(response.text, 'lxml')
                obj = xmltodict.parse(response.content)
                df = pd.DataFrame(obj['sdnList']['sdnEntry'])

            path = data_Path + source_note

            fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ last_update_date + '.csv'
            # df.to_csv(fName_raw, index=False)

            df = df.rename(columns={'uid': 'listingId'})
            df['listingId'] = "OFAC-" + df['listingId']
            df['listingId'] = df['listingId'].apply(lambda x: [x])

            df = df.rename(columns={'idList': 'identificationsList'})
            df['identificationsList'] = df['identificationsList'].apply(extract_ids)

            df['dateOfBirthList'] = df['dateOfBirthList'].apply(extract_dobs)
            df['placeOfBirthList'] = df['placeOfBirthList'].apply(extract_dobs_place)
            df['addressList'] = df['addressList'].apply(extract_addresses)
            df['akaList'] = df['akaList'].apply(extract_akas)
            df = df.rename(columns={"dateOfBirthList": "DOB"})
            df['DOB'] = df['DOB'].apply(try_dob_parse_OFAC)

            df = df.rename(columns={"sdnType": "EntityType"})
            df['EntityType'] = df['EntityType'].str.replace("Entity", "Organization")

            df['programList'] = df['programList'].apply(extract_programs)
            df['programList'] = df['programList'].apply(lambda x: [x] if not isinstance(x, list) else x)
            df = df.rename(columns={"programList": "program"})

            df = df.rename(columns={"firstName": "FirstName", "lastName":"LastName"})
            df['FirstName'] = df['FirstName'].fillna("")
            df['LastName'] = df['LastName'].fillna("")
            df['FullName'] = df['FirstName'] + " " + df['LastName']
            df['FullName'] = df['FullName'].str.strip()
            mask = df['FullName'].apply(lambda x: x.isascii())
            df.loc[mask, 'FullName'] = df.loc[mask, 'FullName'].str.title()
            df['FullName'] = df['FullName'].replace('',np.nan,regex = True)

            df['listName'] = "OFAC Consolidated List"
            df['listName'] = df['listName'].apply(lambda x: [x])


            df['Address'] = df['addressList'].apply(OFAC_address_process)
            df['originalCountryText'] = df['addressList'].apply(OFAC_get_countries)
            df['Countries'] = df['addressList'].apply(OFAC_get_country_codes)

            df['akaList'] = df['akaList'].apply(get_akas)
            df['Alias'] = df['akaList'].apply(OFAC_alias_process)

            df['gender'] = df['identificationsList'].apply(get_gender)
            df['nationality'] = df['nationalityList'].apply(get_nationality)
            df['passport'] = df['identificationsList'].apply(get_passport_info)
            df['additionalSanctionsInformation'] = df['identificationsList'].apply(get_additional_sanctions_info)
            df['offense'] ='Sanction'
            df['offense'] = df['offense'].apply(lambda x: [x])

            AdditionalFields = additional_fields(df, c_mapper)
            df = df.rename(columns=c_mapper)

            df['AdditionalFields'] = AdditionalFields
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df['ReasonOrSanctionType'] = None
            df['EffectiveOrConvictionDate'] = None
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = URL
            df['LatestUpdate'] = LatestUpdate
            df = df.dropna(subset=['FullName'])
            df['NewAoD'] = False
            df['RecordStatus'] = ''

            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId
            df = df.drop_duplicates(subset="SocureId", keep="first")

            df = df[generalized_cols]

            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            isExist = os.path.exists( data_Path + "senzing/" + source_note)
            if not isExist:
                os.makedirs( data_Path + "senzing/" + source_note)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            logger.info(f"oldFile: {oldFile}, previous_update_date: {previous_update_date} ")
            #df = df[:-5]
            if previous_update_date and os.path.exists(oldFile):
                # print("")
                logger.info(f"old file {oldFile} for runId {current_run_id}")
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")
            #df.to_csv(fName, index=False)


            update_for_senzing("", source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

            logger.info(f"Success for {source_note} for runId {current_run_id} total records: {noOfRecords}")

            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write("0")
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', "0" , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of ofac_non_sdn_list for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])


if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])