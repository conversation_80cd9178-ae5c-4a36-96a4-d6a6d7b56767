from typing import Optional, Dict, List
import os
import warnings
import time
import sys
warnings.simplefilter('ignore')
import traceback
from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json

from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import logging
import traceback

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_proxy

#-----------------------------------------------------------------------------------------------
# Module for FBI Wanted List

us_states = {'al': 'alabama', 'ak': 'alaska', 'az': 'arizona', 'ar': 'arkansas', 'ca': 'california', 'co': 'colorado', 'ct': 'connecticut', 'de': 'delaware', 'fl': 'florida', 'ga': 'georgia', 'hi': 'hawaii', 'id': 'idaho', 'il': 'illinois', 'in': 'indiana', 'ia': 'iowa', 'ks': 'kansas', 'ky': 'kentucky', 'la': 'louisiana', 'me': 'maine', 'md': 'maryland', 'ma': 'massachusetts', 'mi': 'michigan', 'mn': 'minnesota', 'ms': 'mississippi', 'mo': 'missouri', 'mt': 'montana', 'ne': 'nebraska', 'nv': 'nevada', 'nh': 'new hampshire', 'nj': 'new jersey', 'nm': 'new mexico', 'ny': 'new york', 'nc': 'north carolina', 'nd': 'north dakota', 'oh': 'ohio', 'ok': 'oklahoma', 'or': 'oregon', 'pa': 'pennsylvania', 'ri': 'rhode island', 'sc': 'south carolina', 'sd': 'south dakota', 'tn': 'tennessee', 'tx': 'texas', 'ut': 'utah', 'vt': 'vermont', 'va': 'virginia', 'wa': 'washington', 'wv': 'west virginia', 'wi': 'wisconsin', 'wy': 'wyoming'}

def get_country_code_us_fbi(text):
    try:
        values = text.lower().split(', ')
        found = False
        for val in values:
            if val in us_states.keys() or val in us_states.values():
                found = True
                break
        if found:
            return ['US']
        else:
            return get_country_code(text.replace("Hondorus", "Honduras").replace("Columbia", "Colombia"))
    except:
        return []

def clean_html(html_text):
    try:
        clean_text = re.sub(r'<.*?>', '', html_text)
        clean_text = ' '.join(clean_text.split())
        return clean_text
    except:
        return ""

def try_date_parse_FBI(xs, dayF = False, yearF = False):
    try:
        dobs = []
        for x in xs:
            d = parse(x, dayfirst=dayF, yearfirst=yearF).date()
            year = str(d.year) if x.find(str(d.year))!=-1 else "0000"
            if year!="0000":
                x = x.replace(year, "0000")

            # date_obj = datetime.strptime(d, "%Y-%m-%d")
            fm = d.strftime("%B")
            sm = d.strftime("%b")

            month = str(d.month)
            month = "0"+month if len(month)==1 else month
            month = month if x.find(fm)!=-1 or x.find(month)!=-1 or x.find(sm)!=-1 else "00"
            day = str(d.day)
            day = "0"+day if len(day)==1 else day
            day = day if x.find(str(d.day))!=-1 else "00"

            d = year + "-" + month + "-" + day
            if d.find("0000")==-1:
                dobs.append(d)
        return dobs
    except:
        return []


def fbi_wanted_list(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL="https://www.fbi.gov/wanted"

        source_note = 'fbi_wanted_list'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        source_url = "https://api.fbi.gov/wanted/v1/list"
        d = get_current_time()
        LatestUpdate = d

        c_mapper = {"title": "FullName", "dates_of_birth_used": "DOB", "publication": "EffectiveOrConvictionDate",
                    "aliases": "Alias", "subjects": "ReasonOrSanctionType", "url": "Url",
                    "hair": "hairColor", "hair_raw": "hairRaw", "description": "offenseDescription",
                    "height_min": "heightMin", "height_max":"heightMax", "place_of_birth": "placeOfBirth",
                    "sex": "gender", "modified": "amendOn", "eyes":"eyeColor", "weight_max": "weightMax",
                    "weight_min": "weightMin"}

        SourceCode = 'FBIWL'
        SourceName = 'FBI Wanted List'

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'
        session = requests.Session()
        session.verify = False
        session.proxies = get_proxy()
        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            df = pd.DataFrame()
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                response = session.get('https://api.fbi.gov/wanted/v1/list')
                logger.info(f"http response status of v1/list :{response.status_code} for runId {run_id}")
                data = json.loads(response.content)
                pages = int(data['total']/20)+2

                response = session.get('https://api.fbi.gov/wanted/v1/list', params={
                    'page': 1
                })
                logger.info(f"http response status of v1/list page 1 :{response.status_code} for runId {run_id}")
                d = json.loads(response.content)
                data = d['items']

                for p in range(2, pages):
                    time.sleep(3)
                    response = session.get('https://api.fbi.gov/wanted/v1/list', params={
                        'page': p
                    })
                    logger.info(f"http response status of v1/list page {p} :{response.status_code} for runId {run_id}")
                    d = json.loads(response.content)
                    data = data + d['items']
                    time.sleep(1)

                df = pd.DataFrame(data)

                fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'
                # df.to_csv(fName_raw, index=False)

            df = df.rename(columns=c_mapper)
            df = df.fillna("")
            df['FullName'] = df['FullName'].str.strip()
            df['FirstName'] = None
            df['LastName'] = None
            df['EntityType'] = "Individual"
            df['DOB'] = df['DOB'].apply(try_date_parse_FBI)
            df['Address'] = df['placeOfBirth'].astype(str)
            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(try_date_parse)
            df['amendOn'] = df['amendOn'].apply(try_date_parse)
            df['ReasonOrSanctionType'] = df['ReasonOrSanctionType'].astype(str).str.replace("[", "").str.replace("]", "").str.replace("'", "")
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = df['Url']
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df['NewAoD'] = False
            df['Address'] = df['Address'].str.strip()
            df['Countries'] = df['Address'].apply(get_country_code_us_fbi)
            df['Address'] = df['Address'].apply(lambda x: [x] if len(x)>0 else [])
            df['Alias'] = df['Alias'].fillna("")
            df['Alias'] = df['Alias'].astype(str).str.replace("[", "").str.replace("]", "").str.replace("'", "").str.replace('"', "").str.replace("”", "").str.replace("“", "").str.split(", ")
            df['Alias'] = df['Alias'].apply(lambda x: x if len(x[0])>0 else [])
            df['RecordStatus'] = ''
            df['enforcementAgency'] = 'Federal Bureau of Investigation'
            df['enforcementAgency'] = df['enforcementAgency'].apply(lambda x: [x])
            df['enforcementType'] = 'wanted'
            df['enforcementType'] = df['enforcementType'].apply(lambda x: [x])
            df['offense'] = 'Warning'
            df['offense'] = df['offense'].apply(lambda x: [x])
            df['offenseDescription'] = df['offenseDescription'].apply(clean_html)
            df['remarks'] = df['remarks'].apply(clean_html)
            df['details'] = df['details'].apply(clean_html)
            df['caution'] = df['caution'].apply(clean_html)


            columns_to_drop = ['files', 'poster_classification', 'status', 'path', 'uid', 'field_offices',
                               'reward_max', 'images', 'reward_min', 'eyes_raw', 'age_range', 'age_min',
                               'person_classification', 'race_raw', 'age_max']
            df = df.drop(columns_to_drop, axis=1)

            additional_fields_mapper = {"title": "FullName", "dates_of_birth_used": "DOB", "aliases": "Alias", "url": "Url"}
            AdditionalFields = additional_fields(df, additional_fields_mapper)
            df['AdditionalFields'] = AdditionalFields

            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            logger.info(f"Success for {source_note} for runId {run_id}")
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            logger.info(f"number of records {source_note} for runId {run_id}: {noOfRecords}")
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of fbi_wanted_list for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])