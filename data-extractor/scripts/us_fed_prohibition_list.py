from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from io import BytesIO
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import logging
import traceback

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_proxy

#-----------------------------------------------------------------------------------------------
# Module for United States Federal Reserve Prohibition from Banking

def us_fed_prohibition_list(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL="https://www.federalreserve.gov/supervisionreg/enforcementactions.htm"
        source_note = 'us_fed_prohibition_list'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        source_url = "https://www.federalreserve.gov/supervisionreg/files/enforcementactions.csv"

        d = get_current_time()
        LatestUpdate = d

        c_mapper = {"Individual": "FullName", "Effective Date": "EffectiveOrConvictionDate",
                    "Action": "ReasonOrSanctionType", "URL": "relatedURL", "Name": "documentType",
                    "Termination Date": "TerminationDate"}

        SourceCode = 'USFEDP'
        SourceName = 'United States Federal Reserve Prohibition from Banking'

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                storage_options = {'User-Agent': 'Mozilla/5.0'}
                proxies = get_proxy()
                response = requests.get(source_url, proxies=proxies, headers=storage_options)
                logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
                df = pd.read_csv(BytesIO(response.content))

                fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'
                # df.to_csv(fName_raw, index=False)

            df = df.rename(columns=c_mapper)
            df = df.fillna("")
            df['FullName'] = df['FullName'].str.strip()
            df['FirstName'] = None
            df['LastName'] = None
            df['EntityType'] = "Individual"
            for i in range(len(df)):
                if df.iloc[i]['FullName'] == "":
                    df.at[i, 'FullName'] = df.iloc[i]['Banking Organization']
                    df.at[i, 'EntityType'] = "Organization"
            df['FullName'] = df['FullName'].str.strip()
            df['DOB'] = [[] for _ in range(len(df))]
            df['Address'] = [[] for _ in range(len(df))]
            df['Countries'] = 'US'
            df['Countries'] = df['Countries'].apply(lambda x: [x])
            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(try_date_parse)
            df['ReasonOrSanctionType'] = df['ReasonOrSanctionType']
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = URL
            df['relatedURL'] = "https://www.federalreserve.gov" + df['relatedURL']
            df['relatedURL'] = df['relatedURL'].str.replace("https://www.federalreserve.govhttps://www.federalreserve.gov", "https://www.federalreserve.gov")
            df['enforcementAgency'] = 'FEDERAL RESERVE SYSTEM'
            df['enforcementAgency'] = df['enforcementAgency'].apply(lambda x: [x])
            df['offense'] = 'Warning'
            df['offense'] = df['offense'].apply(lambda x: [x])
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df['NewAoD'] = False
            df['Alias'] = [[] for _ in range(len(df))]
            df['RecordStatus'] = ''
            additional_fields_mapper = {"Individual": "FullName"}
            AdditionalFields = additional_fields(df, additional_fields_mapper)
            df['AdditionalFields'] = AdditionalFields

            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])

            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} total records {noOfRecords}")
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of us_fed_prohibition_list for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])