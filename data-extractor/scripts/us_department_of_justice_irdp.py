from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')
import re
import logging
from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
import traceback
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import time
import logging
import traceback

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()

from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_proxy

global current_runId

#-------------------------------------------------------------------------------------------------------------
# Module for United States Department of Justice Executive Office for Immigration Review Disciplined Practitioners

def us_department_of_justice_irdp(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols
        current_runId = run_id

        URL = "https://www.justice.gov/eoir/list-of-currently-disciplined-practitioners"

        source_url = URL

        source_note = 'us_department_of_justice_irdp'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        c_mapper = {"Name": "FullName", "City/State": "Address",
                    "Effective Date of Discipline": "EffectiveOrConvictionDate",
                    "Final Discipline Imposed": "otherInfo", "Reinstated?": "reinstated"}

        SourceCode = 'USDJIRDP'
        SourceName = 'United States Department of Justice Executive Office for Immigration Review Disciplined Practitioners'

        d = get_current_time()
        LatestUpdate = d
        try:
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                "Accept-Language": "en-US,en;q=0.9",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive"
            }
            proxies = get_proxy()
            response = requests.request('GET', URL, headers=headers, verify=False, proxies=proxies)
            # print status code
            logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")

            soup = BeautifulSoup(response.text, 'lxml')
            LatestUpdate = try_date_parse_hour_min(soup.find_all("p", {"class":"rteright"})[0].text.strip())
        except:
            LatestUpdate = d

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            logger.info(f"new update available for runId {run_id}")
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'

            df = pd.DataFrame()
            if is_manual_update == 'true':
                logger.info(f"looking for source file at {local_source_path + source_note + '/source_data.csv'} for run_id {run_id}")
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                time.sleep(3)
                proxies = get_proxy()
                """
                response = requests.request('GET', URL, headers=headers, verify=False, proxies=proxies)

                # print status code
                logger.info(f"Status Code for scraping new update us_department_of_justice_irdp: {str(response.status_code)}")

                soup = BeautifulSoup(response.text, 'lxml')
                logger.info(f"response text {response.text}")
                
                updated_date_element = soup.find("div", {"class":"node-updated-date"}).text
                logger.info(f"updated_date_element {updated_date_element}")
                LatestUpdate = try_date_parse_hour_min(format_date_from_html(updated_date_element))
                """
                logger.info(f"LatestUpdate {LatestUpdate} for runId {run_id}")
                tb = soup.find("table", {"class": "font-minus-1"})
                source_cols = [t.text.strip() for t in tb.find_all("th")]
                source_cols.append('relatedURL')
                data = []
                for table_rows in tb.find_all("tr")[1:]:
                    infos = [td.text.strip() for td in table_rows.find_all('td')]
                    try:
                        relatedURL = [['https://www.justice.gov/'+td['href'] for td in table_rows.find_all('a')]]
                    except:
                        relatedURL = []
                    infos += relatedURL
                    data.append(pd.DataFrame([infos], columns=source_cols))

                df = pd.concat(data, ignore_index=True)
                #USE BELOW LINE FOR CREATING SOURCE_DATA
                #df.to_csv(fName_raw, index=False)

            df = df.rename(columns=c_mapper)
            df = df.drop(columns=['Date Immed. Suspension Imposed'])
            df = df.fillna('')
            df['FirstName'] = ""
            df['LastName'] = ""
            df['EntityType'] = "Individual"
            df['FullName'] = df['FullName']
            df['FullName'] = df['FullName'].str.strip()
            df['FullName'] = df['FullName'].str.title()
            df['DOB'] = [[] for _ in range(len(df))]
            df['Address'] = df['Address'].apply(lambda x: [x])
            df['Countries'] = 'US'
            df['Countries'] = df['Countries'].apply(lambda x: [x])
            df['otherInfo'] = df['otherInfo'].apply(lambda x: [x])
            df['Alias'] = [[] for _ in range(len(df))]
            df['ReasonOrSanctionType'] = None
            df['EffectiveOrConvictionDate'] = df["EffectiveOrConvictionDate"].str.replace(" (PDF)", "").apply(try_date_parse)
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = source_url
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df = df.dropna(subset=['FullName'])
            df = df.iloc[:-1]
            df['NewAoD'] = False
            df['RecordStatus'] = ''
            additional_fields_mapper = {"NAME": "FullName", "CITY/STATE": "Address"}
            AdditionalFields = additional_fields(df, additional_fields_mapper)
            df['AdditionalFields'] = AdditionalFields

            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} total records : {noOfRecords}")
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of us_department_of_justice_irdp for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

def format_date_from_html(date_text, format_string="%Y%m%d"):
    date_match = re.search(r"(\bJanuary|\bFebruary|\bMarch|\bApril|\bMay|\bJune|\bJuly|\bAugust|\bSeptember|\bOctober|\bNovember|\bDecember)\s+(\d{1,2}),\s+(\d{4})", date_text)
    logger.info(f"date_match {date_match} for runId {current_runId}")
    if date_match:
        # Build the date string from the regex groups and parse it
        date_str = f"{date_match.group(1)} {date_match.group(2)} {date_match.group(3)}"
        date_str = " ".join(date_str.split())  # Ensure there are no extra spaces
        try:
            date_obj = datetime.strptime(date_str, "%B %d %Y")  # Notice the space between %B and %d and %d and %Y
            # Format the date as 'MM YYYY'
            return date_obj.strftime(format_string)
        except ValueError as e:
            logging.error(f" failure log {traceback.format_exc()} ")
            return "Error in date format"
    else:
        logger.info (f"Date format not found or incorrect for runId {current_runId}")
        return "Date format not found or incorrect"


if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])