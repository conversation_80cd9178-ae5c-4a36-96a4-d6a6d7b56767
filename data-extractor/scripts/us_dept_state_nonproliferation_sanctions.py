from typing import Optional, Dict, List
import os
import warnings
import sys
import traceback

warnings.simplefilter('ignore')

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from tabula import read_pdf
import logging
import traceback
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import tabula
import re

import base64
import requests
import fitz
import glob

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_country_code, get_proxy

#-------------------------------------------------------------------------------------------------------------
# Module for United States Department of State Nonproliferation Sanctions List

#-----------------------------------------------------------------------------------------------

# Set the JAVA_HOME environment variable. Might need to change or comment those out based on the machine.
java_home_path = '/usr/lib/jvm/jre-1.8.0-openjdk'
#java_home_path = '/Library/Internet Plug-Ins/JavaAppletPlugin.plugin/Contents/Home'
os.environ['JAVA_HOME'] = java_home_path

def convert_pdf_to_image(pdf_path, output_dir):
    os.makedirs(output_dir, exist_ok=True)

    # Convert PDF to images (one image per page)
    doc = fitz.open(pdf_path)

    # Save each page as an image file
    for page_num in range(len(doc)):
        image_filename = f"page_{page_num + 1}.png"
        page = doc.load_page(page_num)
        pix = page.get_pixmap(dpi=100)  # Adjust DPI as needed
        image_path = os.path.join(output_dir, image_filename)
        pix.save(image_path)
        # print(f"Saved: {image_path}")

    print("Converted pdf to image file successfully.")

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

def extract_data_using_open_ai(img, api_key):
    try:
        base64_image = encode_image(img)

        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        promptText = """I have an image containing a table of sanctions information. I want to extract the information from the table and structure it as a Python list of dictionaries. Each dictionary should represent one row from the table, where the keys are the column headers, and the values are the corresponding data from each row. The final output should be structured such that it can easily be converted into a Pandas DataFrame.

        The columns in the table are:

        Sanction Name
        Entity
        Location of Entity
        Date Imposed
        Status/Date of Expiration
        Federal Register Notice
        Each row represents a sanction case, and please ensure that any text, links, or other information are captured clearly under these columns as it is without removing anything. Please provide a list of dictionaries where each dictionary contains the data for one row.

        For example, the output should look something like this (fill the data for each row accordingly):
        sanction_data = [
            {
                "Sanction Name": "Executive Order 13382",
                "Entity": "Iran Space Research Center",
                "Location of Entity": "Iran",
                "Date Imposed": "9/3/2019",
                "Status/Date of Expiration": "Active",
                "Federal Register Notice": "Vol. 84, No. 231, 12/02/19"
            },
            {
                "Sanction Name": "Executive Order 13382",
                "Entity": "Astronautics Research Institute [aka Astronautics Systems Research Center, Aerospace Research Institute]",
                "Location of Entity": "Iran",
                "Date Imposed": "9/3/2019",
                "Status/Date of Expiration": "Active",
                "Federal Register Notice": "Vol. 84, No. 231, 12/02/19"
            }
            # Continue for each row in the table...
        ]"""

        payload = {
            "model": "gpt-4o-mini",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": promptText
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ],
            "max_tokens": 3000
        }
        response = requests.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload)
    except:
        txt = "API failed for " + img
        print(txt)
        response = txt

    try:
        st = response.json()['choices'][0]['message']['content'].find('sanction_data =')
        et = response.json()['choices'][0]['message']['content'].find(']\n```\n\n')
        return pd.DataFrame(eval(response.json()['choices'][0]['message']['content'][st+15:et+1]))
    except:
        print("Failed for image", img)
        return False

def run_open_ai_for_bulk(img_folder, api_key):
    files_path = img_folder + '*.png'
    total_files = len(glob.glob(files_path))
    print("Total Images", total_files)

    responses = []
    dfs = []
    for i in range(1, total_files):
        img = img_folder + "page_" + str(i) + ".png"
        # print(img)
        d = extract_data_using_open_ai(img, api_key)
        # display(d)
        if isinstance(d, pd.DataFrame):
            dfs.append(d)

    return pd.concat(dfs, ignore_index=True)

def extract_aliases(names):
    name_dict = {}
    name_list = []
    aka_list = []
    for name in names:
        original_name = name
        akas = []

        # Extracting 'Aka' or 'Also Known As' using regex
        aka_matches = re.findall(r'\[Aka[:\.]?.*?\]|\(Aka[:\.]?.*?\)|\[Also Known As[:\.]?.*?\]', name, re.IGNORECASE)
        # print(aka_matches)
        if aka_matches:
            for match in aka_matches:
                # akas.extend(re.findall(r'Aka: ([^\]]+)', match) or re.findall(r'Aka ([^\)]+)', match) or re.findall(r'Also Known As: ([^\]]+)', match))
                # for match in matches:
                aka1 = re.findall(r'Aka: ([^\]]+)', match, re.IGNORECASE)  # Look for 'Aka: ...'
                aka2 = re.findall(r'Aka ([^\)]+)', match, re.IGNORECASE)   # Look for 'Aka ...'
                aka3 = re.findall(r'Also Known As: ([^\]]+)', match, re.IGNORECASE)  # Look for 'Also Known As: ...'
                aka1 = re.findall(r'Aka. ([^\]]+)', match, re.IGNORECASE)  # Look for 'Aka. ...

                if aka1:
                    akas.extend(aka1)
                elif aka2:
                    akas.extend(aka2)
                elif aka3:
                    akas.extend(aka3)

                name = re.sub(re.escape(match), '', name)

        # Clean up the name and aka list
        name = name.strip()
        akas = [aka.strip() for aka in akas]

        name_list.append(name)
        aka_list.append(akas)

        name_dict[name] = akas

    return name_list, aka_list

def us_dept_state_nonproliferation_sanctions(run_id, is_manual_update, api_key):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL = "https://www.state.gov/bureau-of-international-security-and-nonproliferation/nonproliferation-sanctions"
        source_note = 'us_dept_state_nonproliferation_sanctions'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        c_mapper = {"Entity": "FullName", "Location of Entity": "Address",
                    "Date Imposed": "EffectiveOrConvictionDate", "Sanction Name": "ReasonOrSanctionType",
                    "Federal Register Notice": "designationAct"}

        SourceCode = 'USDSNSL'
        SourceName = 'United States Department of State Nonproliferation Sanctions List'

        d = get_current_time()
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36'
        }

        proxies = get_proxy()
        response = requests.request('GET', URL, headers = headers, verify=False, proxies=proxies)
        logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
        soup = BeautifulSoup(response.text, 'lxml')

        source_url = soup.find("a", {"class": "link-downloadable-content__link"})['href']
        pdf_response = requests.get(source_url, headers=headers, verify=False)
        pdf_response.raise_for_status()  # This will ensure an HTTPError is raised for bad responses
        pdf_path = "/tmp/us_dept_state_nonproliferation_sanctions.pdf"
        #pdf_path = "us_dept_state_nonproliferation_sanctions.pdf"
        output_dir = "/tmp/output_images/"

        with open(pdf_path, 'wb') as f:
            f.write(pdf_response.content)

        LatestUpdate = ""
        dt = tabula.read_pdf(pdf_path, encoding='utf-8', pages='all', lattice = True, multiple_tables=True, pandas_options={'header': None})
        # dt = tabula.read_pdf(pdf_path, stream=True, pages='1', multiple_tables=True, guess=False, pandas_options={'header': None})
        for value in dt[0].values:
            for val in value:
                if str(val).find("Updated")!=-1 or str(val).find("updated")!=-1:
                    try:
                        LatestUpdate = dparser.parse(val, fuzzy=True).strftime("%Y-%m-%dT%H:%M:%S")
                    except:
                        pass

            if LatestUpdate!="":
                break

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36'
                }

                proxies = get_proxy()
                response = requests.request('GET', "https://www.state.gov/key-topics-bureau-of-international-security-and-nonproliferation/nonproliferation-sanctions/", headers = headers, verify=False, proxies=proxies)
                logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
                soup = BeautifulSoup(response.text, 'lxml')

                source_url = soup.find("a", {"class": "link-downloadable-content__link"})['href']

            else:
                fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'
                # print(fName_raw)

                # Converting pdf to images.
                convert_pdf_to_image(pdf_path, output_dir)

                df = run_open_ai_for_bulk(output_dir, api_key)
                df.to_csv(fName_raw, index=False)

            df = df.rename(columns=c_mapper)
            df = df.fillna('')
            df['FirstName'] = ""
            df['LastName'] = ""
            df['EntityType'] = "Organization"
            df['FullName'] = df['FullName'].str.strip()
            df['FullName'] = df['FullName'].str.title()
            df['FullName'] = df['FullName'].str.replace(" And Any Successor, Sub-Unit, Or Subsidiary Thereof", "").str.replace(" And Any Successor, Sub- Unit, Or Subsidiary Thereof", "").str.replace("And Any Successor, Subunit, Or Subsidiary Thereof", "").str.replace("And Its Sub-Units And Successors", "").str.replace("And Any Successor", "")
            df['FullName'] = df['FullName'].str.strip().str.strip(',')
            names, akas = extract_aliases(df['FullName'].values)
            df['FullName'] = names
            df['FullName'] = df['FullName'].str.replace(";", "").str.strip()
            df['DOB'] = [[] for _ in range(len(df))]
            df['Address'] = df['Address'].str.strip()
            df['Countries'] = df['Address'].apply(get_country_code)
            df['Address'] = df['Address'].apply(lambda x: [x] if len(x)>0 else [])
            df['Alias'] = akas
            df['Alias'] = ["".join(val) for val in df['Alias'].values]
            df['Alias'] = df['Alias'].str.replace("Aka", ";").str.split(";")
            df['Alias'] = [[val.replace(',', '').strip() for val in vals] for vals in df['Alias'].values]
            df['Alias'] = df['Alias'].values.tolist()
            df['Alias'] = df['Alias'].apply(lambda x: x if len(x[0])>0 else [])
            df['ReasonOrSanctionType'] = df['ReasonOrSanctionType'].str.strip()
            df['EffectiveOrConvictionDate'] = df["EffectiveOrConvictionDate"].apply(try_date_parse)
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = URL
            df['relatedUrl'] = source_url
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df = df.dropna(subset=['FullName'])
            df['NewAoD'] = False
            df['RecordStatus'] = ''
            df['offense'] = "Sanction"
            df['offense'] = df['offense'].apply(lambda x: [x])

            additional_fields_mapper = {"Entity": "FullName", "Location ofEntity": "Address"}
            AdditionalFields = additional_fields(df, additional_fields_mapper)
            df['AdditionalFields'] = AdditionalFields

            # SocureId = form_SocureId(df, SourceCode)
            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId

            df = df.drop_duplicates(subset="SocureId", keep="first")
            df = df[generalized_cols]
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} total records {noOfRecords}")
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of us_dept_state_nonproliferation_sanctions for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])
if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3], sys.argv[4])