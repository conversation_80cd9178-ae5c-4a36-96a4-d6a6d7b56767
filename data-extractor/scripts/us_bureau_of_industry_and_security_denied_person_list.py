from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import logging
import traceback

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_proxy


#-----------------------------------------------------------------------------------------------
# Module for United States Bureau of Industry and Security The Denied Persons List

def try_date_parse_usbis(x, dayF = False, yearF = False):
    try:
        x = x.replace("00/", "")
        if len(x)==4:
            d = parse(x, dayfirst=dayF, yearfirst=yearF).date()
            # .strftime("%Y-00-00")
        else:
            d = parse(x, dayfirst=dayF, yearfirst=yearF).date()
            # .strftime("%Y-%m-%d")
        year = str(d.year) if x.find(str(d.year))!=-1 else "0000"
        if year!="0000":
            x = x.replace(year, "0000")
        month = str(d.month)
        month = "0"+month if len(month)==1 else month
        month = month if x.find(month)!=-1 else "00"
        day = str(d.day)
        day = "0"+day if len(day)==1 else day
        day = day if x.find(day)!=-1 else "00"

        d = [year + "-" + month + "-" + day]
    except:
        d = []
    return d

def us_bureau_of_industry_and_security_denied_person_list(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL="https://www.bis.doc.gov/index.php/policy-guidance/lists-of-parties-of-concern/denied-persons-list"
        source_note = 'us_bureau_of_industry_and_security_denied_person_list'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        d = get_current_time()
        LatestUpdate = d

        c_mapper = {"name": "FullName", "addresses": "Address", "alt_names": "Alias",
                    "dates_of_birth": "DOB", "start_date": "EffectiveOrConvictionDate",
                    "type": "EntityType"}

        try:
            u = "https://www.trade.gov/consolidated-screening-list"
            proxies = get_proxy()
            response = requests.request('GET', u, headers={'User-Agent': 'Mozilla/5.0'}, data={}, verify=False, proxies=proxies)
            logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
            soup = BeautifulSoup(response.text, 'lxml')
            tag = soup.find("div", {"class": "layout__region--content"})
            url_dict = {}
            for t in tag.find_all("a"):
                url_dict[t.text] = t['href']
            source_url = url_dict['CSV Download']
        except:
            source_url = "https://data.trade.gov/downloadable_consolidated_screening_list/v1/consolidated.csv"

        SourceCode = 'USBISDPL'
        SourceName = 'United States Bureau of Industry and Security The Denied Persons List'

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                df = pd.read_csv(source_url)
                df = df[df["source"]=="Denied Persons List (DPL) - Bureau of Industry and Security"].reset_index(drop=True)
                df = df.drop(columns=["source", "source_information_url"])
                fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'
                # df.to_csv(fName_raw, index=False)
            df = df.rename(columns=c_mapper)
            df = df.rename(columns={"end_date": "removalDate", "federal_register_notice": "designationAct", "standard_order": "sanctionType"})
            df = df.dropna(axis=1, how='all')
            df['EntityType'] = "Individual"
            df['FirstName'] = ""
            df['LastName'] = ""
            df['FullName'] = df['FullName']
            df['FullName'] = df['FullName'].str.strip()
            df['FullName'] = df['FullName'].str.title()
            df['FullName'] = df['FullName'].replace('',np.nan,regex = True)
            if 'DOB' in df.columns:
                df['DOB'] = df['DOB'].astype(str).apply(try_date_parse_usbis)
            else:
                df['DOB'] = [[] for _ in range(len(df))]
            df['Address'] = df['Address'].apply(lambda x: [x])
            df['Countries'] = 'US'
            df['Countries'] = df['Countries'].apply(lambda x: [x])
            df['Alias'] = [[] for _ in range(len(df))]
            df['ReasonOrSanctionType'] = None
            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].astype(str).apply(try_date_parse)
            df['designationDate'] = df['EffectiveOrConvictionDate']
            df['sanctionType'] = df['sanctionType'].str.replace("Y","Standard")
            df['sanctionType'] = df['sanctionType'].str.replace("N","Non Standard")
            df['offense'] ='Sanction'
            df['offense'] = df['offense'].apply(lambda x: [x])
            df['issuingAuthority'] ='Bureau of Industry and Security'
            df['issuingAuthority'] = df['issuingAuthority'].apply(lambda x: [x])

            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = URL
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df = df.dropna(subset=['FullName'])
            df['NewAoD'] = False
            df['RecordStatus'] = ''
            AdditionalFields = additional_fields(df, c_mapper)
            df['AdditionalFields'] = AdditionalFields
            # df = df.astype({'AdditionalFields': str})
            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId

            df = df.drop_duplicates(subset="SocureId", keep="first")
            df = df[generalized_cols]
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id}")
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
        return df
    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of us_bureau_of_industry_and_security_denied_person_list for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])