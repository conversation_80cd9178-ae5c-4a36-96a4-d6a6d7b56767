from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
from functools import lru_cache
import pycountry
import logging
import traceback

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion_v2, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, get_country_code, get_proxy


#-----------------------------------------------------------------------------------------------
# Module for UK HM Treasury Office of Financial Sanctions Implementation Consolidated List

generalized_cols = ['SocureId', 'EntityType', 'FirstName', 'LastName', 'FullName',
                    'DOB', 'Address', 'Countries', 'Alias', 'EffectiveOrConvictionDate',
                    'ReasonOrSanctionType', 'SourceName', 'SourceCode', 'Url',
                    'LatestUpdate', 'AdditionDate', 'DeletionDate', 'NewAoD', 'AdditionalFields', 'RecordStatus']

def check_for_addition_and_deletion(df, LatestUpdate, oldFile):
    print(oldFile)
    previous = pd.read_parquet(oldFile)
    previous['NewAoD'] = False
    previous = previous.rename(columns={'DateOfBirth': 'DOB'})
    previous['DOB'] = previous['DOB'].apply(lambda x: x.tolist())
    if 'Countries' not in previous.columns:
        previous['Countries'] = [[] for _ in range(len(previous))]
    df = df[generalized_cols]
    merged = pd.concat([previous, df], ignore_index=True)
    merged = merged.drop_duplicates(subset=['SocureId'], keep='first')
    merged['matched_prev'] = merged['SocureId'].isin(previous['SocureId'])
    merged['matched_df'] = merged['SocureId'].isin(df['SocureId'])

    merged = merged.set_index(['SocureId'])
    if merged.shape[0]!=df.shape[0]:
        new_idx = merged[merged['matched_prev']==False].index
        deleted_idx = merged[merged['matched_df']==False].index
    else:
        new_idx = []
        deleted_idx = []

    merged['RecordStatus'] = ''
    for idx in new_idx:
        merged.at[idx, 'AdditionDate'] = LatestUpdate
        merged.at[idx, 'RecordStatus'] = 'Add'
        merged.at[idx, 'NewAoD'] = True

    for indx in deleted_idx:
        if pd.isnull(merged.at[indx, 'DeletionDate']) or merged.at[indx, 'DeletionDate'] == None or merged.at[indx, 'DeletionDate'] == "":
            merged.at[indx, 'DeletionDate'] = LatestUpdate
            merged.at[indx, 'RecordStatus'] = 'Delete'
            merged.at[indx, 'NewAoD'] = True

    if len(merged[merged["NewAoD"]==True])>0:
        print("New updates are available!")
    else:
        print("No new Update!")

    merged['LatestUpdate'] = LatestUpdate
    return merged.reset_index()

def concat_hm_treasury_names(s1,s2,s3,s4,s5,s6):
    l = [s1,s2,s3,s4,s5,s6]
    l = [i for i in l if type(i) == str ]
    return ' '.join(l)


def concat_hm_treasury_address(s1,s2,s3,s4,s5,s6, s7, s8):
    l = [s1,s2,s3,s4,s5,s6, s7, s8]
    l = [i for i in l if type(i) == str ]
    return ', '.join(l)

def keep_unique(elements):
    ordered_set = {}
    for element in elements:
        if element!='':
            ordered_set[element] = None
    ordered_list = list(ordered_set.keys())
    return sorted(ordered_list)


def clean_alias(d):
    aliases = []
    for v in d:
        try:
            v = v.replace('„', '').replace('”', '').replace(',', '')
            v = v.strip()
            aliases.append(v)
        except:
            aliases.append(v)
    return aliases

def UKHM_get_passport_info(df):
    passports_info = []
    for i in range(len(df)):
        try:
            if len(df.iloc[i]['Passport Number'][0])>1 and len(df.iloc[i]['Passport Details'][0]):
                c_info = "(Number): "+ df.iloc[i]['Passport Number'][0] + ", (Details): " + df.iloc[i]['Passport Details'][0]
            elif len(df.iloc[i]['Passport Number'][0])>1:
                c_info = "(Number): "+ df.iloc[i]['Passport Number'][0]
            else:
                c_info = ""
            c_info = c_info.strip()

            if c_info!="":
                passports_info.append([c_info])
            else:
                passports_info.append([])
        except:
            passports_info.append([])

    return passports_info


@lru_cache(maxsize=None)
def get_country_code(country_name):
    try:
        country_name = country_name.lower().replace("'", "")
        if 'burma' in country_name:
            country_name = 'myanmar'
        elif 'gaza' in country_name or 'palestinian' in country_name:
            country_name = 'palestine'

        return pycountry.countries.search_fuzzy(country_name)[0].alpha_2
    except LookupError:
        return None

def UKHM_get_countries(data):
    country_codes = set()
    for item in data:
        if 'country' in item:
            country_codes.add(item['country'])
    return sorted(list(country_codes))

def UKHM_get_country_codes(data):
    country_codes = set()
    for item in data:
        code = get_country_code(item)
        if code:
            country_codes.add(code)
    return sorted(list(country_codes))

def uk_hm_treasury_financial_sanctions(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL="https://www.gov.uk/government/publications/financial-sanctions-consolidated-list-of-targets/consolidated-list-of-targets"
        source_url = URL

        source_note = 'uk_hm_treasury_financial_sanctions'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file

        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        c_mapper = {"UK Sanctions List Date Designated": "EffectiveOrConvictionDate", "DOB":"DOB",
                    "Group Type": "EntityType"}


        SourceCode = 'UKHMFSI'
        SourceName = 'UK HM Treasury Office of Financial Sanctions Implementation Consolidated List'

        d = get_current_time()
        LatestUpdate = d
        try:
            proxies = get_proxy()
            response = requests.request('GET', URL, headers={}, data={}, verify=False, proxies=proxies)
            logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
            soup = BeautifulSoup(response.text, 'lxml')
            l = [i.get('href') for i in soup.find_all('a')]
            l = [i for i in l if '.csv' in i]
            source_url = l[0]
            last_update_date = pd.read_csv(source_url,nrows = 0)
            LatestUpdate = try_date_parse_hour_min(last_update_date.columns[1])

        except:
            LatestUpdate = d

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
                # df = pd.read_csv('../entity-resolution/input/uk_hm_treasury_financial_sanctions/source_data.csv')
            else:
                df = pd.read_csv(source_url,header = 1)



            fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'
            # df.to_csv(fName_raw, index=False)
            df = df.rename(columns=c_mapper)
            df['EntityType'] = df['EntityType'].str.replace("Entity", "Organization")
            df['FirstName'] = None
            df['LastName'] = None
            df['FullName'] = df.apply(lambda x:concat_hm_treasury_names(x['Name 1'],x['Name 2'],x['Name 3'],x['Name 4'],x['Name 5'],x['Name 6']),axis = 1)
            df['FullName'] = df['FullName'].str.strip()
            mask = df['FullName'].apply(lambda x: x.isascii())
            df.loc[mask, 'FullName'] = df.loc[mask, 'FullName'].str.title()
            df['FullName'] = df['FullName'].replace('',np.nan,regex = True)
            df['DOB'] = df['DOB'].astype(str).apply(try_date_parse)
            df['Address'] = df.apply(lambda x:concat_hm_treasury_address(x['Address 1'],x['Address 3'],x['Address 3'],x['Address 4'],x['Address 5'],x['Address 6'],x['Country'], x['Post/Zip Code']),axis = 1)
            df = df.fillna("")

            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(try_date_parse)
            df['Last Updated'] = df['Last Updated'].apply(try_date_parse)
            df['Listed On'] = df['Listed On'].apply(try_date_parse)

            df = df.groupby('Group ID').agg(lambda x: list(x)).reset_index()

            gcols = ['EntityType']

            for col in df.columns[1:]:
                df[col] = df[col].apply(lambda x: [item for item in x if item not in ('', None)])
                # print(col)
                df[col] = df[col].apply(keep_unique)
                if col in gcols:
                    df[col] = df[col].apply(lambda x: x[0] if len(x)>0 else "")

            df['Alias'] = None
            df['Alias'] = df['FullName'].apply(lambda x: x[1:])
            df['FullName'] = df['FullName'].apply(lambda x: x[0])
            df['Alias'] = df['Alias'].apply(clean_alias)
            df['Alias'] = df['Alias'] + df['Name Non-Latin Script']


            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(lambda x: [x[0]] if len(x)>0 else [])
            df['amendedOn'] = df['Last Updated'].apply(lambda x: [x[-1]] if len(x)>0 else [])
            df['designationDate'] = df['EffectiveOrConvictionDate']

            df['offense'] ='Sanction'
            df['offense'] = df['offense'].apply(lambda x: [x])
            df['passport'] = UKHM_get_passport_info(df)

            columns_to_drop = ['Name 6', 'Name 1', 'Name 2', 'Name 3', 'Name 4', 'Name 5', 'Name Non-Latin Script', 'Non-Latin Script Type',
                               'Alias Type', 'Alias Quality', 'Last Updated', 'Passport Number', 'Passport Details',
                               'Address 1', 'Address 2', 'Address 3', 'Address 4', 'Address 5', 'Address 6', 'Non-Latin Script Language', 'Listed On']
            column_to_rename = {'Title':'title', 'Position': 'function', 'Group ID':'listingId', 'Other Information':'otherInformation',
                                'NaalGender':'gender', 'Country': 'originalCountryText', 'Post/Zip Code':'zipCode',
                                'National Identification Number':'identificationNumber', 'National Identification Details': 'identificationDetails',
                                'Country of Birth':'placeOfBirth', 'Nationality':'nationality', 'Regime': 'program'}

            df = df.drop(columns_to_drop, axis=1)
            df = df.rename(columns=column_to_rename)
            df['Countries'] = df['originalCountryText'].apply(UKHM_get_country_codes)
            df['ReasonOrSanctionType'] = None
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = URL
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df['FirstName'] = ""
            df['LastName'] = ""
            df = df.dropna(subset=['FullName'])
            df['NewAoD'] = False
            df['RecordStatus'] = ''
            AdditionalFields = additional_fields(df, c_mapper)
            df['AdditionalFields'] = AdditionalFields
            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            logger.info(f"{source_note} old fileName {oldFile}")
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)
            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} total records : {noOfRecords}")
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of uk_hm_treasury_financial_sanctions for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])