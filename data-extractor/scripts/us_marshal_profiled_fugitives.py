from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import re
import time
import logging
import traceback

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()

from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, check_for_addition_and_deletion_v2, get_proxy


#-------------------------------------------------------------------------------------------------------------
# Module for United States Marshals Fugitives

us_states = {'al': 'alabama', 'ak': 'alaska', 'az': 'arizona', 'ar': 'arkansas', 'ca': 'california', 'co': 'colorado', 'ct': 'connecticut', 'de': 'delaware', 'fl': 'florida', 'ga': 'georgia', 'hi': 'hawaii', 'id': 'idaho', 'il': 'illinois', 'in': 'indiana', 'ia': 'iowa', 'ks': 'kansas', 'ky': 'kentucky', 'la': 'louisiana', 'me': 'maine', 'md': 'maryland', 'ma': 'massachusetts', 'mi': 'michigan', 'mn': 'minnesota', 'ms': 'mississippi', 'mo': 'missouri', 'mt': 'montana', 'ne': 'nebraska', 'nv': 'nevada', 'nh': 'new hampshire', 'nj': 'new jersey', 'nm': 'new mexico', 'ny': 'new york', 'nc': 'north carolina', 'nd': 'north dakota', 'oh': 'ohio', 'ok': 'oklahoma', 'or': 'oregon', 'pa': 'pennsylvania', 'ri': 'rhode island', 'sc': 'south carolina', 'sd': 'south dakota', 'tn': 'tennessee', 'tx': 'texas', 'ut': 'utah', 'vt': 'vermont', 'va': 'virginia', 'wa': 'washington', 'wv': 'west virginia', 'wi': 'wisconsin', 'wy': 'wyoming'}

def get_country_code_us_marshal(text):
    try:
        values = text.lower().split(', ')
        found = False
        for val in values:
            if val in us_states.keys() or val in us_states.values():
                found = True
                break
        if found:
            return ['US']
        else:
            return get_country_code(text.replace("Hondorus", "Honduras").replace("Columbia", "Colombia"))
    except:
        return []

def try_dob_parse_us_marshal(x, dayF = False, yearF = False):
    dobs = []
    try:
        x = x.replace("00/", "")
        if len(x)==4:
            d = parse(x, dayfirst=False, yearfirst=False).date()
        else:
            d = parse(x, dayfirst=dayF, yearfirst=yearF).date()

        year = str(d.year) if x.find(str(d.year))!=-1 else "0000"
        if year!="0000":
            x = x.replace(year, "0000")

        # date_obj = datetime.strptime(d, "%Y-%m-%d")
        fm = d.strftime("%B")
        sm = d.strftime("%b")

        month = str(d.month)
        month = "0"+month if len(month)==1 else month
        month = month if x.find(fm)!=-1 or x.find(month)!=-1 or x.find(sm)!=-1 else "00"
        day = str(d.day)
        day = "0"+day if len(day)==1 else day
        day = day if x.find(str(d.day))!=-1 else "00"

        d = year + "-" + month + "-" + day
        if d.find("0000")==-1:
            dobs.append(d)
        return dobs
    except Exception as e:
        # print(e)
        return []

def us_marshal_profiled_fugitives(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL = "https://www.usmarshals.gov/what-we-do/fugitive-apprehension/profiled-fugitives"
        source_url = URL

        source_note = 'us_marshal_profiled_fugitives'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file

        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        c_mapper = {"name": "FullName", "Aliases": "Alias", "Date of Birth": "DOB", "Place of Birth": "placeOfBirth",
                    "Wanted For": "ReasonOrSanctionType", "Date of Warrant": "EffectiveOrConvictionDate"}

        SourceCode = 'USMF'
        SourceName = 'United States Marshals Fugitives'
        source_url = URL

        d = get_current_time()
        LatestUpdate = d

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'
                proxies = get_proxy()
                response = requests.request('GET', URL, verify=False, proxies=proxies)
                logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
                soup = BeautifulSoup(response.text, 'lxml')
                s = soup.find("a", {"aria-label": "Last page"})
                numbers = [int(n) for n in re.findall(r'\d+\.\d+|\d+', s['href'])]
                pages = numbers[0]+1

                entities = []
                for p in range(pages):
                    time.sleep(3)
                    c_url = "https://www.usmarshals.gov/what-we-do/fugitive-apprehension/profiled-fugitives?page=" + str(p)
                    proxies = get_proxy()
                    res = requests.request('GET', c_url, verify=False, proxies=proxies)
                    c_soup = BeautifulSoup(res.text, 'lxml')
                    tags = c_soup.find_all("a", {"class": "usa-link"})
                    entities += ["https://www.usmarshals.gov"+tag['href'] for tag in tags]

                details = []
                for en in entities:
                    proxies = get_proxy()
                    en_response = requests.request('GET', en, verify=False, proxies=proxies)
                    en_soup = BeautifulSoup(en_response.text, 'lxml')

                    content = en_soup.find_all("article")
                    detail = {}

                    try:
                        name = content[0].find("h1").text.strip()
                    except:
                        break

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-wanted-for"})
                        wanted_for = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        wanted_for = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-aliases"})
                        aliases = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        aliases = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-reward"})
                        reward = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        reward = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-date-of-birth"})
                        dob = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        dob = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-place-of-birth"})
                        pob = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        pob = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-wanted-in"})
                        wanted_in = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        wanted_in = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-sex"})
                        sex = c.find("div", {"class": "fugitivedetails-content"}).text.strip()
                    except:
                        sex = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-race-ethnicity"})
                        race = c.find("div", {"class": "fugitivedetails-content"}).text.strip().replace("\n ", "")
                    except:
                        race = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-eyes"})
                        eyes = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        eyes = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-hair"})
                        hair = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        hair = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-weight"})
                        weight = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        weight = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-height"})
                        height = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        height = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-skin-tone"})
                        skin_tone = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        skin_tone = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-scar-tattoo"})
                        scar = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        scar = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-warrant-date"})
                        dow = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        dow = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-warrant-issued"})
                        warrant_issued = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        warrant_issued = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivefield-fugitive-warrant-case-nr"})
                        warrant_num = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        warrant_num = ""

                    try:
                        c = content[0].find("div", {"class": "block-field-blocknodefugitivebody"})
                        case_outline = c.find("div", {"class": "padding-y-1"}).text.strip()
                    except:
                        case_outline = ""

                    detail = {"name": name, "Wanted For": wanted_for, "Aliases": aliases, "reward": reward,
                              "Date of Birth": dob, "Place of Birth": pob, "wantedIn": wanted_in, "gender": sex, "race": race,
                              "eyeColor": eyes, "hairColor": hair, "weight": weight, "height": height, "marks": scar,
                              "skinTone": skin_tone, "Date of Warrant":dow, "warrantOrCaseIssued":warrant_issued,
                              "WarrantOrCase#":warrant_num, "otherInfo": case_outline}

                    details.append(detail)

                data_dict = {}
                for key in list(details[0].keys()):
                    data_dict[key] = []

                for data in details:
                    for key in data.keys():
                        data_dict[key].append(data[key])

                df = pd.DataFrame()
                for key in data_dict.keys():
                    df[key] = data_dict[key]
                df['Url'] = entities[:len(df)]

                # df.to_csv(fName_raw, index=False)

            df = df.rename(columns=c_mapper)
            df = df.fillna('')
            df['FirstName'] = ""
            df['LastName'] = ""
            df['EntityType'] = "Individual"
            df['FullName'] = df['FullName']
            df['FullName'] = df['FullName'].str.strip()
            df['FullName'] = df['FullName'].str.title()
            df['DOB'] = df['DOB'].apply(try_dob_parse_us_marshal)
            df['Address'] = df['placeOfBirth'].str.strip()
            df['Countries'] = df['Address'].apply(get_country_code_us_marshal)
            df['Address'] = df['Address'].apply(lambda x: [x] if len(x)>1 else [])
            df['Alias'] = df['Alias'].str.replace(";",",").str.replace('"', "").str.split(", ")
            df['Alias'] = df['Alias'].apply(lambda x: x if len(x[0])>0 else [])
            df['ReasonOrSanctionType'] = df['ReasonOrSanctionType']
            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(try_date_parse)
            df['enforcementAgency'] = 'United States Marshals'
            df['enforcementAgency'] = df['enforcementAgency'].apply(lambda x: [x])
            df['enforcementType'] = 'wanted'
            df['enforcementType'] = df['enforcementType'].apply(lambda x: [x])
            df['offense'] = 'Warning'
            df['offense'] = df['offense'].apply(lambda x: [x])
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            if not is_manual_update:
                df['Url'] = entities[:len(df)]
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df = df.dropna(subset=['FullName'])
            df['NewAoD'] = False
            df['RecordStatus'] = ''
            additional_fields_mapper = {"name": "FullName", "Aliases": "Alias", "Date of Birth": "DOB"}
            AdditionalFields = additional_fields(df, additional_fields_mapper)
            df['AdditionalFields'] = AdditionalFields

            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId

            df = df.drop_duplicates(subset="SocureId", keep="first")
            df = df[generalized_cols]
            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id} total records {noOfRecords}")
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of us_marshal_profiled_fugitives for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])