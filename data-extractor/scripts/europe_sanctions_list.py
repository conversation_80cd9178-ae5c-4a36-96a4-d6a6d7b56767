from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from io import BytesIO
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
from ast import literal_eval
import re
import pycountry
import logging
import traceback
ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion_v2, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, get_proxy
#-----------------------------------------------------------------------------------------------
# Module for EU External Action Service - Consolidated list of Sanctions
generalized_cols = ['SocureId', 'EntityType', 'FirstName', 'LastName', 'FullName',
                    'DOB', 'Address', 'Countries', 'Alias', 'EffectiveOrConvictionDate',
                    'ReasonOrSanctionType', 'SourceName', 'SourceCode', 'Url',
                    'LatestUpdate', 'AdditionDate', 'DeletionDate', 'NewAoD', 'AdditionalFields', 'RecordStatus']

def get_country_code(text, Birt_programme):
    country_names = text.split(', ')
    # print(country_names)
    d = []
    try:
        for cn in country_names:
            try:
                country_name = pycountry.countries.get(alpha_3=cn).alpha_2
                if country_name not in d:
                    d.append(country_name)
            except:
                continue

        birth_cn = pycountry.countries.get(alpha_3=Birt_programme[0].strip()).alpha_2
        if birth_cn not in d:
            d.append(birth_cn)

        return d

    except:
        # print(text, "None")
        return d





def check_for_addition_and_deletion(df, LatestUpdate, oldFile):
    print(oldFile)
    previous = pd.read_parquet(oldFile)
    previous['NewAoD'] = False
    previous = previous.rename(columns={'DateOfBirth': 'DOB'})
    previous['DOB'] = previous['DOB'].apply(lambda x: x.tolist())
    if 'Countries' not in previous.columns:
        previous['Countries'] = [[] for _ in range(len(previous))]
    df = df[generalized_cols]
    merged = pd.concat([previous, df], ignore_index=True)
    merged = merged.drop_duplicates(subset=['SocureId'], keep='first')
    merged['matched_prev'] = merged['SocureId'].isin(previous['SocureId'])
    merged['matched_df'] = merged['SocureId'].isin(df['SocureId'])

    merged = merged.set_index(['SocureId'])
    if merged.shape[0]!=df.shape[0]:
        new_idx = merged[merged['matched_prev']==False].index
        deleted_idx = merged[merged['matched_df']==False].index
    else:
        new_idx = []
        deleted_idx = []

    merged['RecordStatus'] = ''
    for idx in new_idx:
        merged.at[idx, 'AdditionDate'] = LatestUpdate
        merged.at[idx, 'RecordStatus'] = 'Add'
        merged.at[idx, 'NewAoD'] = True

    for indx in deleted_idx:
        if pd.isnull(merged.at[indx, 'DeletionDate']) or merged.at[indx, 'DeletionDate'] == None or merged.at[indx, 'DeletionDate'] == "":
            merged.at[indx, 'DeletionDate'] = LatestUpdate
            merged.at[indx, 'RecordStatus'] = 'Delete'
            merged.at[indx, 'NewAoD'] = True

    if len(merged[merged["NewAoD"]==True])>0:
        print("New updates are available!")
    else:
        print("No new Update!")

    merged['LatestUpdate'] = LatestUpdate
    return merged.reset_index()

def keep_unique(elements):
    ordered_set = {}
    for element in elements:
        ordered_set[element] = None
    ordered_list = list(ordered_set.keys())
    return sorted(ordered_list)

def clean_alias(d):
    aliases = []
    for v in d:
        try:
            v = v.replace('„', '').replace('”', '').replace(',', '')
            v = v.strip()
            aliases.append(v)
        except:
            aliases.append(v)
    return aliases

def process_address(address):
    return re.sub(r',+', ',', address).strip(" ,").strip(" ")

def EU_get_gender(d):
    try:
        if d[0]=='M':
            return ['male']
        elif d[0]=='F':
            return ['female']
        else:
            return []
    except:
        return []

def convert_alpha3_to_alpha2(alpha3_code):
    try:
        country = pycountry.countries.get(alpha_3=alpha3_code)
        if country:
            return country.alpha_2
        else:
            return None
    except:
        return None

def EU_get_country_code(d):
    try:
        return [convert_alpha3_to_alpha2(code) for code in d if convert_alpha3_to_alpha2(code)]
    except:
        return []



def convert_alpha3_to_country_name(alpha3_code):
    try:
        country = pycountry.countries.get(alpha_3=alpha3_code[0])
        if country:
            return country.name
        else:
            return []
    except:
        return []



def europe_sanctions_list(run_id, is_manual_update):
    """
    """
    try:
        global data_Path
        global generalized_cols

        URL="https://webgate.ec.europa.eu"
        source_note = 'europe_sanctions_list'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file
        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        d = get_today()
        source_url = "https://webgate.ec.europa.eu/fsd/fsf/public/files/csvFullSanctionsList/content?token=n00dgcma"
        if is_manual_update == 'true':
            df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
        else:
            proxies = get_proxy()
            response = requests.get(source_url, proxies=proxies)
            logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
            df = pd.read_csv(BytesIO(response.content),sep = ';')
            # df = pd.read_csv(source_url,sep = ';')
        LatestUpdate = try_date_parse_hour_min(df['Date_file'].iloc[0])

        c_mapper = {"Naal_firstname": "FirstName", "Naal_lastname": "LastName", "Naal_wholename": "FullName",
                    "Subject_type": "EntityType", "Birt_date": "DOB", "Naal_leba_publication_date": "EffectiveOrConvictionDate",
                    }

        SourceCode = 'EEAS'
        SourceName = 'EU External Action Service - Consolidated list of Sanctions'

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            # df = pd.read_csv(source_url,sep = ';')
            fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'
            # df.to_csv(fName_raw, index=False)
            df = df.drop(columns=['Date_file'])
            df = df.rename(columns=c_mapper)
            df = df.fillna("")

            df['EntityType'].replace({'E': 'Organization', 'P': 'Individual'}, inplace=True)
            df['FullName'] = df['FullName'].str.strip()
            mask = df['FullName'].apply(lambda x: x.isascii())
            df.loc[mask, 'FullName'] = df.loc[mask, 'FullName'].str.title()
            df['DOB'] = df['DOB'].astype(str).apply(try_date_parse)
            df['Address'] = df['Addr_number'].astype(str) + ', ' + df['Addr_street'].astype(str) + ', ' + df['Addr_city'].astype(str) + ', ' + df['Addr_country'].astype(str) + ', ' + df['Addr_zipcode'].astype(str)
            df['Address'] = df['Address'].apply(process_address)


            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(try_date_parse)
            df = df.groupby('Entity_logical_id').agg(lambda x: list(x)).reset_index()
            gcols = ['EntityType', 'FirstName', 'LastName', 'Naal_middlename']

            for col in df.columns[1:]:
                df[col] = df[col].apply(lambda x: [item for item in x if item not in ('', None)])
                df[col] = df[col].apply(keep_unique)
                if col in gcols:
                    df[col] = df[col].apply(lambda x: x[0] if len(x)>0 else "")

            df['amendedOn'] = df['EffectiveOrConvictionDate'].apply(lambda x: [x[-1]] if len(x)>1 else [])
            df['EffectiveOrConvictionDate'] = df['EffectiveOrConvictionDate'].apply(lambda x: [x[0]] if len(x)>0 else [])

            df['ReasonOrSanctionType'] = None
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = URL
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df['NewAoD'] = False
            df['Alias'] = None
            df['Alias'] = df['FullName'].apply(lambda x: x[1:])
            df['FullName'] = df['FullName'].apply(lambda x: x[0])
            df['RecordStatus'] = ''
            df['Alias'] = df['Alias'].apply(clean_alias)


            processed_columns = {}
            columns_to_drop = []
            for col in df.columns:
                if col.find('logical_id')!=-1:
                    columns_to_drop.append(col)
                elif col.find('_')!=-1:
                    c = col.replace('_', ' ').title().replace(' ', '').strip()
                    processed_columns[col] = c
                    # print(c)
            df = df.drop(columns_to_drop, axis=1)
            df = df.rename(columns=processed_columns)

            df['designationDate'] = df['EffectiveOrConvictionDate']
            df['offense'] ='Sanction'
            df['offense'] = df['offense'].apply(lambda x: [x])
            df['issuingAuthority'] = "European Union"
            df['issuingAuthority'] = df['issuingAuthority'].apply(lambda x: [x])

            df['NaalGender'] = df['NaalGender'].apply(EU_get_gender)
            df['BirtCountry'] = df['BirtCountry'].apply(convert_alpha3_to_country_name)
            df['IdenCountry'] = df['IdenCountry'].apply(convert_alpha3_to_country_name)

            column_to_rename = {'LebaUrl':'relatedURL', 'LebaNumtitle': 'designationAct', 'EuRefNum':'listingId', 'EntityRemark':'otherInformation', 'NaalGender':'gender',
                                'BirtCountry':'placeOfBirth', 'IdenCountry':'nationality', 'Programme': 'program'}
            df = df.rename(columns=column_to_rename)

            df['Countries'] = df['AddrCountry'].apply(EU_get_country_code)
            AdditionalFields = additional_fields(df, c_mapper)
            df['AdditionalFields'] = AdditionalFields
            SocureId = create_unique_ids(df)
            df['SocureId'] = SocureId
            df = df.drop_duplicates(subset="SocureId", keep="first")

            df = df[generalized_cols]

            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id}")
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            logger.info(f"number of records {source_note} for runId {run_id}: {noOfRecords}")
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of europe_sanctions_list for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])