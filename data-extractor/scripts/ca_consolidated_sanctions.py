from typing import Optional, Dict, List
import os
import warnings
import sys
warnings.simplefilter('ignore')

from bs4 import BeautifulSoup
import bs4
import requests
import pandas as pd
import numpy as np
import json
from datetime import date
from datetime import datetime, timedelta
from dateutil.parser import parse
# dparse necessary for canada as parse is used by xml.
import dateutil.parser as dparser
import hashlib
import xmltodict
from datadog import initialize, statsd
import ast
import ssl
import re
from ast import literal_eval
import logging
import traceback
from functools import lru_cache
import pycountry

ssl._create_default_https_context = ssl._create_unverified_context

host = os.environ.get('METRICS_COLLECTOR_HOST', '127.0.0.1')
port = int(os.environ.get('METRICS_COLLECTOR_PORT', 8125))
options = {
    'statsd_host': host,
    'statsd_port': port
}
initialize(**options)
logging.basicConfig(
    level=logging.INFO,
    format='| %(asctime)s | %(levelname)s | (%(filename)s:%(funcName)s:%(lineno)d) | %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger()
from utils import check_for_addition_and_deletion_v2, get_country_code, try_date_parse, try_dob_parse, try_date_parse_OFAC, try_date_parse_gbi, get_today, get_current_time, try_date_parse_hour_min, additional_fields, form_SocureId, return_sha_has, create_unique_ids, is_new_update_available, check_for_addition_and_deletion, process_trailling_commas_in_string, find_serial_numbers, handle_exception, update_for_senzing, data_Path, generalized_cols, local_source_path, get_proxy, http_with_retries


#-----------------------------------------------------------------------------------------------
# Module for Consolidated Canadian Autonomous Sanctions List
generalized_cols  = ['SocureId', 'EntityType', 'FirstName', 'LastName', 'FullName',
                     'DOB', 'Address', 'Countries', 'Alias', 'EffectiveOrConvictionDate',
                     'ReasonOrSanctionType', 'SourceName', 'SourceCode', 'Url',
                     'LatestUpdate', 'AdditionDate', 'DeletionDate', 'NewAoD', 'AdditionalFields', 'RecordStatus']


@lru_cache(maxsize=None)
def get_country_code(country_name):
    try:
        country_name = country_name.lower().replace("'", "")
        if 'burma' in country_name:
            country_name = 'myanmar'
        elif 'gaza' in country_name or 'palestinian' in country_name:
            country_name = 'palestine'

        return pycountry.countries.search_fuzzy(country_name)[0].alpha_2
    except LookupError:
        return None

def CA_get_country_codes(data):
    country_codes = set()
    for item in data:
        code = get_country_code(item)
        if code:
            country_codes.add(code)
    return sorted(list(country_codes))

def get_aliases(st):
    try:
        pattern = r'\b\w+:'
        matches = re.findall(pattern, st)
        for m in matches:
            st = st.replace(m+" ", "")
            st = st.replace(m, "")
        st = [s.strip() for s in st.split(',')]
        return st
    except:
        return []

def try_dob_parse_CA(x, dayF = False, yearF = False):
    try:
        x = x.replace("00/", "")
        if len(x)==4:
            d = parse(x, dayfirst=dayF, yearfirst=yearF).date()
        else:
            d = parse(x, dayfirst=dayF, yearfirst=yearF).date()
        year = str(d.year) if x.find(str(d.year))!=-1 else "0000"
        if year!="0000":
            x = x.replace(year, "0000")

        fm = d.strftime("%B")
        sm = d.strftime("%b")
        month = str(d.month)
        month = "0"+month if len(month)==1 else month
        month = month if x.find(fm)!=-1 or x.find(month)!=-1 or x.find(sm)!=-1 else "00"
        day = str(d.day)
        day = "0"+day if len(day)==1 else day
        day = day if x.find(str(d.day))!=-1 else "00"

        d = year + "-" + month + "-" + day
        if d.find("0000")!=-1:
            d = []
        else:
            d = [d]
    except:
        d = []
    return d

def ca_consolidated_sanctions(run_id, is_manual_update):
    """
    """
    try:
        global data_Path

        URL="https://www.international.gc.ca/world-monde/international_relations-relations_internationales/sanctions/consolidated-consolide.aspx"
        source_url = "https://www.international.gc.ca/world-monde/assets/office_docs/international_relations-relations_internationales/sanctions/sema-lmes.xml"

        source_note = 'ca_consolidated_sanctions'
        statsd.increment('scraper.started', tags=['socure:'+source_note])
        os.umask(18) # allow all users to be able to read and write the file

        isExist = os.path.exists(data_Path + source_note)
        if not isExist:
            os.makedirs(data_Path + source_note)

        c_mapper = {"GivenName": "FirstName", "LastName": "LastName", "DateOfBirth": "DOB",
                    "Effective Date": "EffectiveOrConvictionDate", "Reason for Term/Exclusion": "ReasonOrSanctionType"}

        SourceCode = 'CCAS'
        SourceName = 'Consolidated Canadian Autonomous Sanctions List'

        headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3"}


        d = get_current_time()
        LatestUpdate = d
        try:
            proxies = get_proxy()
            response = requests.request('GET', URL, headers=headers, data={}, verify=False, proxies=proxies)
            soup = BeautifulSoup(response.text, 'lxml')
            logger.info(f"http response status of {source_note} {response.status_code} for runId {run_id}")
            last_update_date = dparser.parse(soup.find_all('strong')[0].text,fuzzy=True)
            LatestUpdate = str(last_update_date.year)+'-'+str(last_update_date.month).zfill(2)+'-'+str(last_update_date.day).zfill(2)
            LatestUpdate = try_date_parse_hour_min(LatestUpdate)
        except:
            LatestUpdate = d

        fName = data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv'
        logName = data_Path + source_note + "/last_update.json"
        new_update, previous_update_date = is_new_update_available(logName, LatestUpdate)
        noOfRecords = '0'

        if new_update:
            statsd.increment('scraper.new.update.available', tags=['socure:'+source_note])
            fName_raw = data_Path + source_note + "/" + source_note + '_original'+ '_'+ LatestUpdate + '.csv'

            if is_manual_update == 'true':
                df = pd.read_csv(local_source_path + source_note + '/source_data.csv')
            else:
                proxies = get_proxy()
                response = http_with_retries(source_url, headers, proxies)
                soup = BeautifulSoup(response.text, 'lxml')
                obj = xmltodict.parse(response.content)
                df = pd.DataFrame(obj['data-set']['record'])

            df = df.rename(columns=c_mapper)
            df['Country'] = df['Country'].fillna("")
            df['Country'] = df['Country'].apply(lambda x:x.split(r'/')[0].split('(')[0].strip())
            df = df.rename(columns={'Country': 'originalCountryText'})

            df['offense'] ='Sanction'
            df['offense'] = df['offense'].apply(lambda x: [x])

            df['EntityType'] = "Individual"
            indx = df[df['EntityType'] != "Organization"].index.values
            for i in indx:
                df.at[i, 'EntityType'] = "Individual"
            df['FirstName'] = df['FirstName'].fillna("")
            df['LastName'] = df['LastName'].fillna("")
            df['FullName'] = df['FirstName'] + " " + df['LastName']
            df['FullName'] = df['FullName'].str.strip()
            for i in range(len(df)):
                if df.iloc[i]['FullName'] == "":
                    df.at[i, 'FullName'] = df.iloc[i]['EntityOrShip']
                    df.at[i, 'EntityType'] = "Organization"
            df['FullName'] = df['FullName'].str.strip()
            mask = df['FullName'].apply(lambda x: x.isascii())
            df.loc[mask, 'FullName'] = df.loc[mask, 'FullName'].str.title()
            df['FullName'] = df['FullName'].replace('',np.nan,regex = True)
            df['DOB'] = df['DateOfBirthOrShipBuildDate'].fillna("").astype(str).apply(try_dob_parse_CA)
            df['Address'] = df['originalCountryText']
            df['Address'] = df['Address'].str.split(',')
            df['originalCountryText'] = df['originalCountryText'].apply(lambda x: [x])
            df['Countries'] = df['originalCountryText'].apply(CA_get_country_codes)
            df['Aliases'] = df['Aliases'].apply(get_aliases)
            df = df.rename(columns={'Aliases': 'Alias'})
            df['ReasonOrSanctionType'] = None
            df['EffectiveOrConvictionDate'] = None
            df['SourceName'] = SourceName
            df['SourceCode'] = SourceCode
            df['Url'] = URL
            df['LatestUpdate'] = LatestUpdate
            df['AdditionDate'] = None
            df['DeletionDate'] = None
            df = df.dropna(subset=['FullName'])
            df['NewAoD'] = False
            df['RecordStatus'] = ''
            AdditionalFields = additional_fields(df, c_mapper)
            df['AdditionalFields'] = AdditionalFields


            SocureId = create_unique_ids(df)

            df['SocureId'] = SocureId

            df = df[generalized_cols]

            df.to_csv(data_Path + source_note + "/" + source_note + '_'+ LatestUpdate + '.csv', index=False)

            fmaster = data_Path + "senzing/" + source_note + '_new_master.parquet'
            os.makedirs(os.path.dirname(fmaster), exist_ok=True)
            df.to_parquet(fmaster, index=False, compression='none')
            oldFile = data_Path + "senzing/" + source_note + '_'+ previous_update_date + '.parquet'
            logger.info(f"{source_note} old fileName {oldFile}")
            if previous_update_date and os.path.exists(oldFile):
                df = check_for_addition_and_deletion_v2(df, LatestUpdate, oldFile)

            df = df[generalized_cols]
            df = df.drop_duplicates(subset="SocureId", keep="first")

            senzing_dir = data_Path + source_note + "/dataForSenzing/"
            isExist = os.path.exists(senzing_dir)
            if not isExist:
                os.makedirs(senzing_dir)

            update_for_senzing(senzing_dir, source_note, LatestUpdate, df)
            statsd.increment('scraper.files.created', tags=['socure:'+source_note])
            logger.info(f"Success for {source_note} for runId {run_id}")
            noOfRecords = str(df.shape[0])
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            last_update = {"last_update": LatestUpdate}
            json_object = json.dumps(last_update, indent=4)
            with open(logName, "w") as outfile:
                outfile.write(json_object)
                outfile.close()
            logger.info(f"number of records {source_note} for runId {run_id}: {noOfRecords}")
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])
            return df
        else:
            logger.info(f"No new Update from {source_note} for runId {run_id}")
            with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
                fout.write(noOfRecords)
            statsd.increment('scraper.new.update.unavailable', tags=['socure:'+source_note])
            statsd.increment('scraper.completed', tags=['socure:'+source_note])
            statsd.set('scraper.records.count', noOfRecords , tags=['socure:'+source_note])

    except Exception as err:
        traceback.format_exc()
        tb_str = traceback.format_exc()
        tb_lines = tb_str.splitlines()
        logger.error(f"Failure log of ca_consolidated_sanctions for runId {run_id}: {' '.join(tb_lines)}")
        with open(data_Path + "senzing/" + source_note + '_meta_data.txt', 'w') as fout:
            fout.write("-1")
        statsd.increment('scraper.failed', tags=['socure:'+source_note])

if __name__ == '__main__':
    globals()[sys.argv[1]](sys.argv[2],sys.argv[3])