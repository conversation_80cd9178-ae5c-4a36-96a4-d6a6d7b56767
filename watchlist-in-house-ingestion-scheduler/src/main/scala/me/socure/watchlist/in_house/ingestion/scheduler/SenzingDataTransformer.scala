package me.socure.watchlist.in_house.ingestion.scheduler

import java.io._
import java.nio.charset.StandardCharsets
import java.nio.file.{Files, Path, Paths, StandardOpenOption}

import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.model.ErrorResponse
import me.socure.watchlist.in_house.ingestion.common.formats.WatchlistInHouseIngestionFormats
import me.socure.watchlist.in_house.ingestion.common.models.Constants._
import me.socure.watchlist.in_house.ingestion.common.models.{ESRecord, ErrorResponses, Key, MetaData, ResolvedEntityRecordDetails}
import me.socure.watchlist.in_house.ingestion.data.loader.service.EntityResolvedCSVParser
import me.socure.watchlist.in_house.ingestion.data.loader.utility.{DynamoDBDataHandler, EntityResolvedDataHandler}
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by <PERSON><PERSON> on Jan 12, 2024
 */
class SenzingDataTransformer(entityResolvedCSVParser: EntityResolvedCSVParser) extends WLInHouseTransformer {

  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)
  implicit val jsonFormats: Formats = WatchlistInHouseIngestionFormats.formats
  implicit val ex: ExecutionContext = ExecutionContext.Implicits.global

  private val ELASTIC_SEARCH_INDEX_V1 = "index_1"
  private val DYNAMO_DB_TABLE_V1 = "dynamodb"

  override def transform(inputStream: InputStream, runId: String, sourceId: Int, nonCommentKeys: List[String]): Future[Either[ErrorResponse, Boolean]] = {
    val senzingFileBufferedReader: BufferedReader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
    entityResolvedCSVParser.readCsv(senzingFileBufferedReader).flatMap {
      case Left(error) => Future.successful(Left(error))
      case Right(parsedCSVDetails) =>
        metrics.increment("parsed.entity.resolved.data.success", s"run_id:$runId")
        logger.info(s"successfully parsed entity resolved data for runId: $runId. Total records after parsing: ${parsedCSVDetails.size}")
        val esCompatibleRecords = EntityResolvedDataHandler.convertEntitiesToESCompatibleRecords(parsedCSVDetails, sourceId, runId)

        val updateEntitiesDynamoDBCompatibleRecords = DynamoDBDataHandler.convertEntitiesToDynamoDBRecords(sourceId, getUpdateEntitiesCSVParsedRecords(parsedCSVDetails), getUpdateEntitiesRecords(esCompatibleRecords), runId, nonCommentKeys).foldLeft(Map.empty[String, String])((result, record) => {
          result ++ Map(record._1.split("#")(0) -> record._2)
        })

        val senzingUpdatedEntitiesDataRawPath = TRANSFORMER_JOB_UPDATE_ENTITIES_RAW_PATH + "_" + s"$runId"
        val senzingDeletedEntitiesDataRawPath = TRANSFORMER_JOB_DELETE_ENTITIES_RAW_PATH + "_" + s"$runId"
        val metadataRawPath = TRANSFORMER_JOB_METADATA_RAW_PATH + "_" + s"$runId"

        val senzingUpdateEntitiesDataPath: Path = Paths.get(senzingUpdatedEntitiesDataRawPath)
        val senzingUpdateEntitiesDataParentDir = senzingUpdateEntitiesDataPath.getParent

        val senzingDeleteEntitiesDataPath: Path = Paths.get(senzingDeletedEntitiesDataRawPath)
        val senzingDeleteEntitiesDataParentDir = senzingDeleteEntitiesDataPath.getParent

        val metaDataPath: Path = Paths.get(metadataRawPath)
        val metaDataParentDir = metaDataPath.getParent

        if (!Files.exists(senzingUpdateEntitiesDataParentDir)) {
          Files.createDirectories(senzingUpdateEntitiesDataParentDir)
        }

        if (!Files.exists(senzingDeleteEntitiesDataParentDir)) {
          Files.createDirectories(senzingDeleteEntitiesDataParentDir)
        }

        if (!Files.exists(metaDataParentDir)) {
          Files.createDirectories(metaDataParentDir)
        }

        val senzingUpdateEntitieswriter = Files.newBufferedWriter(senzingUpdateEntitiesDataPath, StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.WRITE)
        val senzingDeleteEntitieswriter = Files.newBufferedWriter(senzingDeleteEntitiesDataPath, StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.WRITE)
        val metaDataWriter = Files.newBufferedWriter(metaDataPath, StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.WRITE)

        try {
          // write update entities
          val updateEntitiesJsonString = mapToJSON(updateEntitiesDynamoDBCompatibleRecords)
          senzingUpdateEntitieswriter.write(updateEntitiesJsonString)
          senzingUpdateEntitieswriter.flush()

          // write delete entities
          getDeleteEntitiesRecords(esCompatibleRecords).foreach {
            entry =>
              val deleteEntitiesJsonString = entry._1.entityId
              senzingDeleteEntitieswriter.write(deleteEntitiesJsonString)
              senzingDeleteEntitieswriter.newLine()
              senzingDeleteEntitieswriter.flush()
          }

          val metadata = Serialization.write(constructMetadataForSenzingData())
          metaDataWriter.write(metadata)
          metaDataWriter.newLine()
          metaDataWriter.flush()
          Future.successful(Right(true))
        }
        catch {
          case ex: Exception =>
            logger.error(s"Error while transforming Senzing resolved data with ex: $ex")
            metrics.increment(s"transform.senzing.resolved.data.error", s"runId:$runId")
            Future.successful(Left(ErrorResponses.SezingDataParseError))
        } finally {
          senzingFileBufferedReader.close()
          senzingUpdateEntitieswriter.close()
          senzingDeleteEntitieswriter.close()
          metaDataWriter.close()
        }
    } recoverWith {
      case ex: Exception =>
        metrics.increment("parsed.senzing.resolved.data.failure", s"class:${ex.getClass}", s"run_id: $runId")
        logger.error(s"Parsing senzing resolved data failed with ex:",ex)
        Future.successful(Left(ErrorResponses.SezingDataParseError))
    }
  }

  private def constructMetadataForSenzingData(): MetaData = {
    MetaData(
      elasticSearchIndex = ELASTIC_SEARCH_INDEX_V1,
      dynamoTable = DYNAMO_DB_TABLE_V1
    )
  }

  private def getUpdateEntitiesCSVParsedRecords(parsedCSVRecords: Map[Key, ResolvedEntityRecordDetails]): Map[Key, ResolvedEntityRecordDetails] = parsedCSVRecords.filter(record => record._2.entityOperation == SENZING_ENTITY_ADD_OPERATION)

  private def getUpdateEntitiesRecords(esCompatibleRecords: Map[Key, ESRecord]): Map[Key, ESRecord] = esCompatibleRecords.filter(record => record._2.entityOperation == SENZING_ENTITY_ADD_OPERATION)

  private def getDeleteEntitiesRecords(esCompatibleRecords: Map[Key, ESRecord]): Map[Key, ESRecord]  = esCompatibleRecords.filter(record => record._2.entityOperation == SENZING_ENTITY_DELETE_OPERATION)

  private def mapToJSON(records: Map[String, String]): String = {
    records.values.mkString(System.lineSeparator())
  }

  override def getDataSource(): String = ""
}
