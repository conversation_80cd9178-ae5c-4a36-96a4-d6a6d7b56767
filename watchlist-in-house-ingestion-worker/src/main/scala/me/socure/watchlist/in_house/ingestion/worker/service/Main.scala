package me.socure.watchlist.in_house.ingestion.worker.service

import com.google.common.util.concurrent.ServiceManager
import com.google.inject.{<PERSON><PERSON><PERSON>, Injector}
import me.socure.acuris.client.AcurisModule
import me.socure.common.clock.RealClockModule
import me.socure.common.config.{EnvironmentConfigurationModule, IntConfigurationModule}
import me.socure.common.environment.{AppNameModule, Environment, EnvironmentModule}
import me.socure.common.executioncontext.factory.{ExecutionContextModule, ThreadPoolExecutorModule}
import me.socure.common.jettythreadpool.factory.JettyThreadPoolModule
import me.socure.common.logs.{EnvironmentLogConfigurationModule, LogConfigurationLoader}
import me.socure.common.microservice.defaults.DefaultMicroservice
import me.socure.senzing.client.module.SenzingModule
import me.socure.watchlist.in_house.ingestion.auditing.module.AuditingModule
import me.socure.watchlist.in_house.ingestion.client.sns.module.WatchlistInHouseIngestionSNSClientModule
import me.socure.watchlist.in_house.ingestion.common.module.{HMACHttpVerifierModule, WLSourceMappingsModule}
import me.socure.watchlist.in_house.ingestion.data.extractor.module.DataExtractorModule
import me.socure.watchlist.in_house.ingestion.data.loader.module.EntityResolvedCSVParserModule
import me.socure.watchlist.in_house.ingestion.data.loader.module.common.DataLoaderModule
import me.socure.watchlist.in_house.ingestion.data.loader.module.dynamodb.DynamoDBModule
import me.socure.watchlist.in_house.ingestion.data.loader.module.opensearch.OpenSearchModule
import me.socure.watchlist.in_house.ingestion.data.loader.module.s3.{AmazonS3ClientModule, S3FileModule, S3ServiceModule, S3AsyncClientModule}
import me.socure.watchlist.in_house.ingestion.scheduler.module.{WLIngestionSchedulerModule, WLTransformerModule}
import me.socure.watchlist.in_house.ingestion.storage.migration.FlywayMigration
import me.socure.watchlist.in_house.ingestion.storage.module.{FlywayMigrationModule, WatchlistInhouseIngestionDaoModule}
import me.socure.watchlist.in_house.ingestion.worker.module.{DynamicControlCenterV2EvaluateModule, MessagesProcessorModule, ServiceManagerModule, WorkerServiceModule}
import net.codingwell.scalaguice.InjectorExtensions._
import org.slf4j.{Logger, LoggerFactory}
import java.util.concurrent.TimeUnit

import scala.util.{Failure, Success, Try}

object Main extends DefaultMicroservice {

  def startUp(args: Array[String]): Unit = {
    val logger: Logger = LoggerFactory.getLogger(getClass)

    Try {
      val injector: Injector = Guice.createInjector(
        new AppNameModule,
        new EnvironmentModule,
        new RealClockModule,
        new EnvironmentConfigurationModule,
        new EnvironmentLogConfigurationModule,
        new IntConfigurationModule("jmx.port"),
        new ThreadPoolExecutorModule,
        new JettyThreadPoolModule,
        new ExecutionContextModule,
        new WorkerServiceModule,
        new MessagesProcessorModule,
        new ServiceManagerModule,
        new HMACHttpVerifierModule,
        new AuditingModule,
        new WatchlistInhouseIngestionDaoModule,
        new FlywayMigrationModule,
        new AmazonS3ClientModule,
        new S3FileModule,
        new S3ServiceModule,
        new S3AsyncClientModule,
        new DynamoDBModule,
        new OpenSearchModule,
        new DataLoaderModule,
        new WatchlistInHouseIngestionSNSClientModule,
        new DataExtractorModule,
        new SenzingModule,
        new AcurisModule,
        new WLIngestionSchedulerModule,
        new EntityResolvedCSVParserModule,
        new DynamicControlCenterV2EvaluateModule,
        new WLTransformerModule,
        new WLSourceMappingsModule
      )

      val logConfigurationLogger = injector.instance[LogConfigurationLoader]
      logConfigurationLogger.load(injector.instance[Environment.Value])

      val flywayMigration = injector.instance[FlywayMigration]
      flywayMigration.execute()

      val serviceManager: ServiceManager = injector.instance[ServiceManager]
      serviceManager.startAsync()
      sys.addShutdownHook {
        serviceManager.stopAsync().awaitStopped(5, TimeUnit.SECONDS)
      }
    } match {
      case Success(_) =>
      case Failure(ex) => {
        logger.error("Unable to start service due to", ex)
        System.exit(1)
      }
    }
  }
}
