<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>watchlist-in-house-ingestion</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <version>${revision}</version>
    <artifactId>watchlist-in-house-ingestion-worker</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.17.1</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-ingestion-scheduler</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-senzing-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-ingestion-auditing</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-ingestion-storage</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-ingestion-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-ingestion-rest</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-ingestion-data-loader</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>watchlist-in-house-ingestion-data-extractor</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- SCALATRA -->
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- JETTY SERVER -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-webapp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.typelevel</groupId>
            <artifactId>cats-core_2.11</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-openapi3-scalatra</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>

        <!-- SOCURE COMMON LIBRARY-->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jetty</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jmx-service</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-environment</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-socure-constants</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-ms-dependencies</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-servlet-metrics</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sqs-fallback</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws.secretsmanager</groupId>
            <artifactId>aws-secretsmanager-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- for prod Smoke Test -->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-impl-db</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-impl-http</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-servlet-api</artifactId>
            <version>${socure.commons.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.13</version>
        </dependency>
        <!-- end for prod Smoke Test -->

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- end for scala test -->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>2.3</version>
                <executions>
                    <!-- Run shade goal on package phase -->
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">
                                    <resource>reference.conf</resource>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>me.socure.watchlist.in_house.ingestion.worker.service.Main</mainClass>
                                </transformer>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                        <exclude>application.json</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>