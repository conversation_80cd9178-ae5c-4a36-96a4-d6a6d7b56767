package me.socure.watchlist.in_house.ingestion.common.models.enums

/**
 * Created by <PERSON><PERSON> on Aug 24, 2023
 */
object EventType extends Enumeration {
  type EventType = Value

  val ADD = Value(1, "add")
  val UPDATE = Value(2, "update")
  val DELETE = Value(3, "delete")

  def byId(id: Int): EventType = values.find(_.id == id).getOrElse(throw new Exception("Invalid Event Type Exception"))
  def isValidEventType(id: Int): Boolean = values.exists(_.id == id)
}
