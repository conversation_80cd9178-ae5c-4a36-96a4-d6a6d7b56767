package me.socure.watchlist.in_house.ingestion.common.utility

import java.time.{LocalDate, LocalDateTime}
import java.time.format.{DateTimeFormatter, DateTimeParseException}
import java.time.temporal.ChronoUnit

/**
 * Created by <PERSON><PERSON> on Nov 17, 2024
 */
object SourceAuditDateUtil {

  private val DATE_FORMAT = "yyyy-MM-dd"
  private val DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss"
  private val HOURS = 23
  private val MINUTES = 59
  private val SECONDS = 59
  private val formatter = DateTimeFormatter.ofPattern(DATE_FORMAT)

  def isValidDateRange(startDate: String, endDate: String): Boolean = {
    val startLocalDate = LocalDate.parse(startDate, formatter)
    val endLocalDate = LocalDate.parse(endDate, formatter)
    val daysDifference = ChronoUnit.DAYS.between(startLocalDate, endLocalDate)
    daysDifference <= 90
  }

  def isValidDateFormat(dateStr: String): Boolean = {
    try {
      LocalDate.parse(dateStr, formatter)
      true
    } catch {
      case _: DateTimeParseException => false
    }
  }

  def formatDate(startDate: String, endDate: String): (String, String) = {
    try {
      val startLocalDate = LocalDate.parse(startDate, formatter).atStartOfDay()
      val endLocalDate = LocalDate.parse(endDate, formatter).atTime(HOURS, MINUTES, SECONDS)
      val sqlDateTimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT)
      val formattedStartDate: String = startLocalDate.format(sqlDateTimeFormatter)
      val formattedEndDate: String = endLocalDate.format(sqlDateTimeFormatter)
      (formattedStartDate, formattedEndDate)
    }
    catch {
      case ex: Exception =>
        throw new IllegalArgumentException("Invalid date format", ex)
    }
  }

  def getLast30DaysStartAndEnd: (String, String) = {
    val endDate = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0)
    val startDate = endDate.minusDays(30)
    (startDate.format(formatter), endDate.format(formatter))
  }

}
