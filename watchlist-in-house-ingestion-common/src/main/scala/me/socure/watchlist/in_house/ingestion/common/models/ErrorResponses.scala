package me.socure.watchlist.in_house.ingestion.common.models

import me.socure.model.ErrorResponse


/**
 * Created by <PERSON><PERSON> on Aug 29, 2023
 */
object ErrorResponses {
  val watchlistSourceAuditError: ErrorResponse = ErrorResponse(100, "Internal Error occurred while adding Source Audit")
  val watchlistEntitiesAuditError: ErrorResponse = ErrorResponse(101, "Internal Error occurred while adding Entities Audit")
  val watchlistSuppressedEntitiesError: ErrorResponse = ErrorResponse(102, "Internal Error occurred while adding Suppressed Entities")
  val watchlistDataExtractionRunDetailsError: ErrorResponse = ErrorResponse(103, "Internal Error occurred while adding Data Extraction Run Details")
  val watchlistEntitySourceMappingError: ErrorResponse = ErrorResponse(104, "Internal Error occurred while adding Entity Source Mapping")
  val watchlistSourceAuditNotFoundError: ErrorResponse = ErrorResponse(105, "No Source Audit found for sourceId")
  val watchlistEntitiesAuditNotFoundError: ErrorResponse = ErrorResponse(106, "No Entities Audit found for EntityId")
  val FileMetadataFetchError: ErrorResponse = ErrorResponse(107,"No FileMetadata available for the given id")
  val InvalidRecordId: ErrorResponse = ErrorResponse(108,"recordId is either empty or invalid")
  val InvalidCountry: ErrorResponse = ErrorResponse(109,"country is invalid")
  val InvalidDOB: ErrorResponse = ErrorResponse(110,"dateOfBirth is invalid")
  val InvalidEntityType: ErrorResponse = ErrorResponse(111,"entityType is invalid")
  val InvalidFullName: ErrorResponse = ErrorResponse(112,"entityName is either empty or invalid")
  val InvalidListedOn: ErrorResponse = ErrorResponse(113,"listedOn is invalid")
  val InvalidDateOfDeath: ErrorResponse = ErrorResponse(114,"dateOfDeath is invalid")
  val InvalidActiveStartDate: ErrorResponse = ErrorResponse(115,"activeStartDate is invalid")
  val InvalidPhoneNumber: ErrorResponse = ErrorResponse(116,"phoneNumbers is invalid")
  val InvalidNumberOfRecords: ErrorResponse = ErrorResponse(117,"customWatchlistRecord is invalid")
  val InvalidZip: ErrorResponse = ErrorResponse(118,"zip is invalid for the input country")
  val ResolvedEntitiesS3FetchError: ErrorResponse = ErrorResponse(119,"Error while fetching resolved entities file from S3")
  val IngestionValidationError: ErrorResponse = ErrorResponse(120,"Error while validating ingestion request")
  val ESIngestionInternalError: ErrorResponse = ErrorResponse(121,"ES Ingestion Internal error")
  val IngestionJobRedisLockAcquireError: ErrorResponse = ErrorResponse(122,"Error while acquiring lock for Ingestion Job")
  val IngestionJobFetchRunDetailsError: ErrorResponse = ErrorResponse(123,"Error while fetching run details" )
  val IngestionJobFetchGlobalRunsError: ErrorResponse = ErrorResponse(124,"Error while fetching global runs" )
  val IngestionJobRedisLockReleaseError: ErrorResponse = ErrorResponse(125,"Error while releasing lock for Ingestion Job")
  val IngestionJobRunStatusUpdateError: ErrorResponse = ErrorResponse(126,"Error while updating run status in Ingestion Job")
  val ElasticSearchVersionConflictError: ErrorResponse = ErrorResponse(127,"Error while deleting records in elastic search due to version conflict")
  val ElasticSearchRecordDeletionError: ErrorResponse = ErrorResponse(128,"Error while deleting records in elastic search")
  val ElasticSearchDocumentNotFoundError: ErrorResponse = ErrorResponse(129,"Error while deleting records in elastic search due to unavailability of document")
  val ElasticSearchIngestionException: ErrorResponse = ErrorResponse(130,"Error while ingesting records in elastic search")
  val ElasticSearchRecordDeleteByQueryError: ErrorResponse = ErrorResponse(131,"Error while deleting records by query in elastic search")
  val DynamoDBAddItemsError: ErrorResponse = ErrorResponse(132,"Error while adding batch items to Dynamo DB")
  val DynamoDBDeleteItemsError: ErrorResponse = ErrorResponse(133,"Error while deleting batch items to Dynamo DB")
  val OtherDataParseException: ErrorResponse = ErrorResponse(134,"Error while parsing other data")
  val fetchWLDataExtractionTargetRunsError: ErrorResponse = ErrorResponse(135,"Error while fetching WL Data Extraction Target runs")
  val WLDataExtractionInProgressRunsUpdateError: ErrorResponse = ErrorResponse(136,"Error while updating WL Data Extraction in Progress runs")
  val WLIngestionJobUpdateFailedStatusError: ErrorResponse = ErrorResponse(137,"Error while updating WL Data Loader failed status")
  val ESDataLoadError : ErrorResponse = ErrorResponse(138,"Error while loading data into ES")
  val DynamoDBDataLoadError : ErrorResponse = ErrorResponse(140,"Error while loading data into DynamoDB")
  val S3LoadDataError : ErrorResponse = ErrorResponse(141,"Error while loading data from S3")
  val EntityResolvedParsingError: ErrorResponse = ErrorResponse(142,"Error while parsing entity resolved file")
  val UpdateRunDetailsError: ErrorResponse = ErrorResponse(143,"Error while updating run details")
  val S3FileUploadFailed: ErrorResponse = ErrorResponse(144,"Error while uploading file to S3")
  val watchlistAuditLogInfoNotFoundError: ErrorResponse = ErrorResponse(145, "Error while fetching Audit log data from inhouse data")
  val fetchS3ObjectError: ErrorResponse = ErrorResponse(146, "Error while fetching s3 object")
  val transformedSenzingDataUploadError: ErrorResponse = ErrorResponse(147, "Error while uploading transformed senzing data")
  val ESCompatibleRecordsUploadError: ErrorResponse = ErrorResponse(148, "Error while uploading transformed ES compatible records")
  val DynamoDBCompatibleRecordsUploadError: ErrorResponse = ErrorResponse(150, "Error while uploading transformed DynamoDB compatible records")
  val AcurisIndividualsDataParseError: ErrorResponse = ErrorResponse(151, "Error while parsing Acuris Individuals data.")
  val AcurisBusinessDataParseError: ErrorResponse = ErrorResponse(152, "Error while parsing Acuris Business data.")
  val TransformedAcurisIndividualsDataUploadError: ErrorResponse = ErrorResponse(153, "Error while uploading transformed Acuris Individuals data.")
  val TransformedAcurisBusinessDataUploadError: ErrorResponse = ErrorResponse(154, "Error while uploading transformed Acuris Business data.")
  val SezingDataParseError: ErrorResponse = ErrorResponse(155, "Error while parsing senzing resolved data")
  val AuditingDateRangeError: ErrorResponse = ErrorResponse(156, "Auditing end date should be greater than start date")
  val FetchAuditRecordsByDateRangeError: ErrorResponse = ErrorResponse(157, "Error while fetching auditing records by date range")
  val EmptyAuditRecordsBYDateRange: ErrorResponse = ErrorResponse(158, "Empty records while fetching auditing records by date range")
}
