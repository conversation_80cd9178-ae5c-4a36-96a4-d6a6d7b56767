package me.socure.watchlist.in_house.ingestion.common.utility

import org.scalatest.{FunSuite, Matchers}

/**
 * Created by <PERSON><PERSON> on Nov 17, 2024
 */
class SourceAuditDateUtilTest extends FunSuite with Matchers {

  test("should succeed for given dates") {
    val startDate = "2024-10-01"
    val endDate = "2024-10-01"
    SourceAuditDateUtil.isValidDateRange(startDate, endDate) shouldBe (true)
  }

  test("should fail for given dates") {
    val startDate = "2024-05-01"
    val endDate = "2024-10-31"
    SourceAuditDateUtil.isValidDateRange(startDate, endDate) shouldBe (false)
  }

  test("should format date for the the valid input and format") {
    val startDate = "2024-10-01"
    val endDate = "2024-10-01"
    SourceAuditDateUtil.formatDate(startDate, endDate) shouldBe ("2024-10-01 00:00:00","2024-10-01 23:59:59")
  }

  test("should format date for the the valid input and format") {
    val invalidInputStartDateFormat = "2023-07-04T08:15:00"
    val invalidInputEndDateFormat = "2024-11-18T14:30:45"
    a[Exception] should be thrownBy {
      SourceAuditDateUtil.formatDate(invalidInputStartDateFormat, invalidInputEndDateFormat)
    }
  }
}
