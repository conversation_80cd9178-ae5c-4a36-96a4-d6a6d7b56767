package me.socure.watchlist.in_house.ingestion.rest.validator

/**
 * Created by <PERSON><PERSON> on Aug 30, 2023
 */
object WatchlistIn_houseIngestionDomainValidation {

  case object InvalidSourceId extends DomainValidation {
    def errorMessage: String = "Source Id should not be empty."
  }

  case object InvalidRunId extends DomainValidation {
    def errorMessage: String = "Run Id should not be empty."
  }

  case object InvalidEntityId extends DomainValidation {
    def errorMessage: String = "Entity Id should not be empty."
  }

  case object InvalidDataExtractionStatus extends DomainValidation {
    override def errorMessage: String = "WL Data Extraction status should not be empty"
  }

  case object InvalidPath extends DomainValidation {
    def errorMessage: String = "Path should not be empty."
  }

  case object InvalidFileName extends DomainValidation {
    def errorMessage: String = "File Name should not be empty."
  }

  case object InvalidDynamoTableName extends DomainValidation {
    override def errorMessage: String = "Invalid table name."
  }

  case object InvalidDate extends DomainValidation {
    def errorMessage: String = "Start Date or End Date should be in yyyy-MM-dd format."
  }

  case object InvalidDateRange extends DomainValidation {
    def errorMessage: String = "Date Range should be within a period of 90 days. Please pass startDate and endDate within a period of 90 days."
  }

}
