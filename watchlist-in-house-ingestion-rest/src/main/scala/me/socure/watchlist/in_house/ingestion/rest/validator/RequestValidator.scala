package me.socure.watchlist.in_house.ingestion.rest.validator

import cats.data.ValidatedNel
import me.socure.common.environment.EnvironmentResolver
import me.socure.watchlist.in_house.ingestion.common.formats.WatchlistInHouseIngestionFormats
import me.socure.watchlist.in_house.ingestion.common.models.Constants.MAX_PAGE_SIZE
import me.socure.watchlist.in_house.ingestion.common.models.enums.EventType
import me.socure.watchlist.in_house.ingestion.common.models.enums.WLDataExtractionStatus.WLDataExtractionStatus
import me.socure.watchlist.in_house.ingestion.common.models.request.{DynamoTableCreateRequest, EntitiesAuditFetchRequest, FileUploadRequest, WLRunDetailsUpdateRequest}
import me.socure.watchlist.in_house.ingestion.common.utility.SourceAuditDateUtil
import org.json4s.Formats

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
/**
 * Created by <PERSON><PERSON> on Aug 30, 2023
 */
object RequestValidator {
  type ValidationResult[A] = ValidatedNel[DomainValidation, A]

  implicit def jsonFormats: Formats = WatchlistInHouseIngestionFormats.formats

  val TableNamePrefix = s"watchlist-in-house-ingestion"

  def validateSourceId(sourceId: String): Either[DomainValidation, Boolean] = Either.cond(
    Option(sourceId).isDefined,
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidSourceId
  )

  def validateRunId(runId: String): Either[DomainValidation, Boolean] = Either.cond(
    Option(runId).isDefined,
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidRunId
  )

  def validateEntityId(entityId: String): Either[DomainValidation, Boolean] = Either.cond(
    Option(entityId).isDefined,
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidEntityId
  )

  def validateWLDataExtractionStatus(status: WLDataExtractionStatus): Either[DomainValidation, Boolean] = Either.cond(
    Option(status).isDefined,
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidDataExtractionStatus
  )

  def validateWLRunDetails(request: WLRunDetailsUpdateRequest): Either[DomainValidation, Boolean] = {
    for {
      _ <- validateRunId(request.runId).right
      _ <- validateWLDataExtractionStatus(request.status).right
    } yield true
  }

  def validatePath(path: String): Either[DomainValidation, Boolean] = Either.cond(
    Option(path).isDefined,
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidPath
  )

  def validateFileName(fileName: String): Either[DomainValidation, Boolean] = Either.cond(
    Option(fileName).isDefined,
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidFileName
  )

  def validateFileUploadRequest(request: FileUploadRequest): Either[DomainValidation, Boolean] = {
    for {
      _ <- validatePath(request.path).right
      _ <- validateFileName(request.fileName).right
    } yield true
  }

  def validateDynamoTableCreateRequest(dynamoTableCreateRequest: DynamoTableCreateRequest): Either[DomainValidation, Boolean] = {
    for {
      _ <- validateTableName(dynamoTableCreateRequest.tableName).right
    } yield  true
  }

  def validateEntitiesAuditFetchRequest(entitiesAuditFetchRequest: EntitiesAuditFetchRequest): Either[DomainValidation, Boolean] = {
    for {
      _ <- validateDate(entitiesAuditFetchRequest.startDate).right
      _ <- validateDate(entitiesAuditFetchRequest.endDate).right
      _ <- validateDateRange(entitiesAuditFetchRequest.startDate, entitiesAuditFetchRequest.endDate).right
    } yield  true
  }

  private def validateDate(date: Option[String]): Either[DomainValidation, Boolean] = Either.cond(
    date.isEmpty || (date.isDefined && SourceAuditDateUtil.isValidDateFormat(date.get)),
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidDate
  )

  private def validateDateRange(startDate: Option[String], endDate: Option[String]): Either[DomainValidation, Boolean] = Either.cond(
    (startDate.isEmpty && endDate.isEmpty) || (startDate.isDefined && endDate.isDefined && SourceAuditDateUtil.isValidDateRange(startDate.get, endDate.get)),
    true,
    WatchlistIn_houseIngestionDomainValidation.InvalidDateRange
  )

  private def validateTableName(tableName: String): Either[DomainValidation, Boolean] = {
    val environment = EnvironmentResolver.resolve().toString
    val tableNamePrefixWithEnv = s"$TableNamePrefix-$environment"
    Either.cond(tableName.startsWith(tableNamePrefixWithEnv), true, WatchlistIn_houseIngestionDomainValidation.InvalidDynamoTableName)
  }
}

