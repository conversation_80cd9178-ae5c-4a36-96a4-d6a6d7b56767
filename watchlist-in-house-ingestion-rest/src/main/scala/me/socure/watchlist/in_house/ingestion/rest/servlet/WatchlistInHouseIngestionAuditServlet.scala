package me.socure.watchlist.in_house.ingestion.rest.servlet

import com.google.inject.Inject
import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.openapi3.scalatra.OpenApiScalatraSupport
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.ErrorResponse
import me.socure.watchlist.in_house.ingestion.auditing.service.AuditingApi
import me.socure.watchlist.in_house.ingestion.common.formats.WatchlistInHouseIngestionFormats
import me.socure.watchlist.in_house.ingestion.common.models.request.{DynamoTableCreateRequest, EntitiesAuditFetchRequest, FileUploadRequest, S3FilesDeleteRequest, WLRunDetailsUpdateRequest}
import me.socure.watchlist.in_house.ingestion.rest.validator.RequestValidator
import org.json4s.Formats
import org.scalatra.json.JacksonJsonSupport
import org.scalatra.{FutureSupport, ScalatraServlet}
import org.slf4j.{Logger, LoggerFactory}
import org.scalatra.servlet.FileUploadSupport

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

/**
 * Created by Soorya Prasad on Aug 29, 2023
 */
class WatchlistInHouseIngestionAuditServlet @Inject()(val hmacVerifier: HMACHttpVerifier,
                                                      val openApi: OpenAPI,
                                                      service: AuditingApi)
                                                     (implicit val executor: ExecutionContext) extends ScalatraServlet with FileUploadSupport
  with JacksonJsonSupport
  with FutureSupport
  with AuthenticationSupport
  with OpenApiScalatraSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = WatchlistInHouseIngestionFormats.formats

  before() {
    contentType = formats("json")
  }

  get("/source/:sourceId") {
    val sourceId = params("sourceId")
    RequestValidator.validateSourceId(sourceId) match {
      case Right(_) =>
        val result = service.getWLSourceAuditInfoBySourceId(sourceId.toInt)
        ScalatraResponseFactory.get(result)
      case Left(error) =>
        metrics.increment("invalid.case.details.request")
        logger.error(s"Validation Failed with error: {}", error.errorMessage)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, error.errorMessage))))
    }
  }

  get("/source/run/:runId") {
    val runId = params("runId")
    RequestValidator.validateRunId(runId) match {
      case Right(_) =>
        val result = service.getWLSourceAuditInfoByRunId(runId)
        ScalatraResponseFactory.get(result)
      case Left(error) =>
        metrics.increment("invalid.case.details.request")
        logger.error(s"Validation Failed with error: {}", error.errorMessage)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, error.errorMessage))))
    }
  }

  get("/entities/:entityId") {
    val entityId = params("entityId")
    val fetchLastAudit = params("fetchLastAudit") match {
      case "true" => true
      case _ => false
    }
    RequestValidator.validateEntityId(entityId) match {
      case Right(_) =>
        val result = service.getWLEntitiesAuditInfoByEntityId(entityId, fetchLastAudit)
        ScalatraResponseFactory.get(result)
      case Left(error) =>
        metrics.increment("invalid.case.details.request")
        logger.error(s"Validation Failed with error: {}", error.errorMessage)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, error.errorMessage))))
    }
  }

  get("/entities_audit") {
    try {
      val startDate: Option[String] = params.get("startDate").flatMap(s => Try(s).toOption)
      val endDate: Option[String] = params.get("endDate").flatMap(s => Try(s).toOption)
      val sourceId: Option[Int] = params.get("sourceId").flatMap(s => Try(s.toInt).toOption)
      val entitySourceChangeType: Option[Int] = params.get("entitySourceChangeType").flatMap(s => Try(s.toInt).toOption)
      val runId: Option[String] = params.get("runId").flatMap(s => Try(s).toOption)
      val page: Option[Int] = params.get("page").flatMap(s => Try(s.toInt).toOption)
      val size: Option[Int] = params.get("size").flatMap(s => Try(s.toInt).toOption)
      val entitiesAuditFetchRequest = EntitiesAuditFetchRequest(sourceId, entitySourceChangeType, runId, startDate, endDate, page, size)
      RequestValidator.validateEntitiesAuditFetchRequest(entitiesAuditFetchRequest) match {
        case Left(error) =>
          metrics.increment("fetch.entities.audit.error", s"class: ${error.getClass}", s"message: ${error.errorMessage}")
          logger.error(s"Validation Failed with error: {}", error.errorMessage)
          ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, error.errorMessage))))
        case Right(_) =>
          ScalatraResponseFactory.get(service.getWLAuditInfoByDateRange(entitiesAuditFetchRequest))
      }
    }
    catch {
      case ex: Exception =>
        logger.error(s"Error while fetching audit logs response. Incoming Request ${request.getRequestURL}?${request.getQueryString} error: ${ex.getMessage}", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(500, ex.getMessage))))
    }
  }

  get("/wlsources") {
    val result = service.getWatchlistSources()
    ScalatraResponseFactory.get(result)
  }

  put("/run_details/update") {
    try {
      val wlRunDetails = parsedBody.extract[WLRunDetailsUpdateRequest]
      RequestValidator.validateWLRunDetails(wlRunDetails) match {
        case Right(_) =>
          ScalatraResponseFactory.get {
            service.updateWlCurrentRunDetails(wlRunDetails)
          }
        case Left(err) =>
          metrics.increment("invalid.run.details.error")
          logger.error(s"WL run details Validation Failed with error: {}", err.errorMessage)
          ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, err.errorMessage))))
      }
    } catch {
      case ex: Exception => {
        metrics.increment("invalid.run.details.exception", "ex:" + ex.getClass.getSimpleName)
        logger.error("Failed to update run details", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
      }
    }
  }

  put("/file") {
    try {
      val fileUploadRequest = FileUploadRequest(path = params("path"), fileName = params("fileName"))
      val ingestionFile = fileParams("ingestion_file")
      RequestValidator.validateFileUploadRequest(fileUploadRequest) match {
        case Right(_) =>
          ScalatraResponseFactory.get {
            service.uploadFile(fileUploadRequest, ingestionFile)
          }
        case Left(err) =>
          metrics.increment("invalid.file.upload.error")
          logger.error(s"File upload validation failed with error: {}", err.errorMessage)
          ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, err.errorMessage))))
      }
    } catch {
      case ex: Exception => {
        metrics.increment("invalid.file.upload.exception", "ex:" + ex.getClass.getSimpleName)
        logger.error("Failed to upload fail", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
      }
    }
  }

  post("/dynamo/create"){
    val dynamoTableCreateRequest = parsedBody.extract[DynamoTableCreateRequest]
    RequestValidator.validateDynamoTableCreateRequest(dynamoTableCreateRequest) match {
      case Right(_) =>  ScalatraResponseFactory.get(service.createTable(dynamoTableCreateRequest))
      case Left(error) =>
        metrics.increment("invalid.dymamo.table.create.error")
        logger.error(s"Dynamo table creation failed with error: {}", error.errorMessage)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, error.errorMessage))))
    }
  }

  post("/s3/delete"){
    val s3FileDeleteRequest = parsedBody.extract[S3FilesDeleteRequest]
    ScalatraResponseFactory.get(service.deleteFiles(s3FileDeleteRequest))
  }
}
