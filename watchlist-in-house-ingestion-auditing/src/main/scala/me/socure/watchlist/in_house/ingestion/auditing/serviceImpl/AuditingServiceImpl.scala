package me.socure.watchlist.in_house.ingestion.auditing.serviceImpl

import javax.inject.Inject
import me.socure.model.ErrorResponse
import me.socure.watchlist.in_house.ingestion.auditing.service.AuditingApi
import me.socure.watchlist.in_house.ingestion.auditing.service.s3.S3Service
import me.socure.watchlist.in_house.ingestion.common.models.{ErrorResponses, WLDataExtractionRunDetails, WLInhouseAuditRecords, WatchlistEntitiesAudit, WatchlistEntitiesAuditRecord, WatchlistEntitySourceMapping, WatchlistSourceAudit, WatchlistSuppressedEntities}
import me.socure.watchlist.in_house.ingestion.storage.dao.WatchlistInhouseIngestionDao
import me.socure.watchlist.in_house.ingestion.common.models.Constants.{AUDIT_DATE_RANGE_BATCH_SIZE, EMPTY_RECORD_COUNT, RUN_ID_ID_INDEX, RUN_ID_SOURCE_ID_SEPARATOR, SOURCE_ID_INDEX}
import me.socure.watchlist.in_house.ingestion.common.models.enums.{WLDataExtractionStatus, WatchlistSources}
import me.socure.watchlist.in_house.ingestion.common.models.enums.WatchlistSources.WatchlistSource
import me.socure.watchlist.in_house.ingestion.common.models.request.{DynamoTableCreateRequest, EntitiesAuditFetchRequest, FileUploadRequest, S3FilesDeleteRequest, WLRunDetailsUpdateRequest}
import me.socure.watchlist.in_house.ingestion.common.models.response.{FileUploadResponse, WatchlistDataExtractionRunDetailsSuccessResponse, WatchlistEntitiesAuditSuccessResponse, WatchlistEntitySourceMappingSuccessResponse, WatchlistSourceAuditSuccessResponse, WatchlistSuppressedEntitiesSuccessResponse}
import org.joda.time.DateTime
import org.scalatra.servlet.FileItem
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model.{AttributeDefinition, BillingMode, CreateTableRequest, CreateTableResponse, KeySchemaElement, KeyType, ScalarAttributeType}

import java.time.{LocalDate, ZonedDateTime}
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import scala.compat.java8.FutureConverters
import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/**
 * Created by Soorya Prasad on Aug 23, 2023
 */
class AuditingServiceImpl @Inject()(wlIn_houseIngestionDao: WatchlistInhouseIngestionDao, s3Service: S3Service, dynamoDbAsyncClient: DynamoDbAsyncClient)
                                   (implicit ec: ExecutionContext) extends AuditingApi {

  private val logger = LoggerFactory.getLogger(getClass)

  private val DynamoTableKey = "entityId"
  private val DynamoEntityDetailsAttribute = "entityDetails"

  override def auditWLSource(watchlistSourceAudit: WatchlistSourceAudit): Future[Either[ErrorResponse, WatchlistSourceAuditSuccessResponse]] = {
    wlIn_houseIngestionDao.insertWLSourceAuditRecord(watchlistSourceAudit).flatMap {
      recordCount =>
        (recordCount > EMPTY_RECORD_COUNT) match {
          case true => Future.successful(Right(WatchlistSourceAuditSuccessResponse(recordCount.toInt)))
          case false => Future.successful(Left(ErrorResponses.watchlistSourceAuditError))
        }
    }
  }

  override def auditWLEntities(watchlistEntitiesAudit: WatchlistEntitiesAudit): Future[Either[ErrorResponse, WatchlistEntitiesAuditSuccessResponse]] = {
    wlIn_houseIngestionDao.insertWLEntitiesAuditRecord(toWatchlistEntitiesAuditRecord(watchlistEntitiesAudit) ).flatMap {
      recordCount =>
        (recordCount > EMPTY_RECORD_COUNT) match {
          case true => Future.successful(Right(WatchlistEntitiesAuditSuccessResponse(recordCount.toInt)))
          case false => Future.successful(Left(ErrorResponses.watchlistEntitiesAuditError))
        }
    }
  }

  private def toWatchlistEntitiesAuditRecord(watchlistEntitiesAudit: WatchlistEntitiesAudit) = {
    WatchlistEntitiesAuditRecord(id = 0,
      entityId = watchlistEntitiesAudit.entityId,
      sourceId = watchlistEntitiesAudit.sourceId,
      originalDataSourceId = watchlistEntitiesAudit.originalDataSourceId,
      sourceEventType = watchlistEntitiesAudit.sourceEventType,
      lastUpdatedRunId = watchlistEntitiesAudit.lastUpdatedRunId,
      lastUpdatedTimestamp = watchlistEntitiesAudit.lastUpdatedTimestamp)
  }

  override def auditWLSuppressedEntities(watchlistSuppressedEntities: WatchlistSuppressedEntities): Future[Either[ErrorResponse, WatchlistSuppressedEntitiesSuccessResponse]] = {
    wlIn_houseIngestionDao.insertWLSuppressedEntitiesRecord(watchlistSuppressedEntities).flatMap {
      recordCount =>
        (recordCount > EMPTY_RECORD_COUNT) match {
          case true => Future.successful(Right(WatchlistSuppressedEntitiesSuccessResponse(recordCount.toInt)))
          case false => Future.successful(Left(ErrorResponses.watchlistSuppressedEntitiesError))
        }
    }
  }

  override def auditWLDataExtractionRunDetails(wlDataExtractionRunDetails: WLDataExtractionRunDetails): Future[Either[ErrorResponse, WatchlistDataExtractionRunDetailsSuccessResponse]] = {
    wlIn_houseIngestionDao.insertWLCurrentRunDetailsRecord(wlDataExtractionRunDetails).flatMap {
      recordCount =>
        (recordCount > EMPTY_RECORD_COUNT) match {
          case true => Future.successful(Right(WatchlistDataExtractionRunDetailsSuccessResponse(recordCount.toInt)))
          case false => Future.successful(Left(ErrorResponses.watchlistDataExtractionRunDetailsError))
        }
    }
  }

  override def auditWLEntitySourceMapping(watchlistEntitySourceMapping: WatchlistEntitySourceMapping): Future[Either[ErrorResponse, WatchlistEntitySourceMappingSuccessResponse]] = {
    wlIn_houseIngestionDao.insertWLEntitySourceMappingRecord(watchlistEntitySourceMapping).flatMap {
      recordCount =>
        (recordCount > EMPTY_RECORD_COUNT) match {
          case true => Future.successful(Right(WatchlistEntitySourceMappingSuccessResponse(recordCount.toInt)))
          case false => Future.successful(Left(ErrorResponses.watchlistEntitySourceMappingError))
        }
    }
  }

  override def getWLSourceAuditInfoBySourceId(sourceId: Int): Future[Either[ErrorResponse, Seq[WatchlistSourceAudit]]] = {
    wlIn_houseIngestionDao.fetchWLSourceAuditInfoBySourceId(sourceId).flatMap {
      records =>
        (records.size > EMPTY_RECORD_COUNT) match {
          case true => Future.successful(Right(records))
          case false => Future.successful(Left(ErrorResponses.watchlistSourceAuditNotFoundError))
        }
    }
  }

  override def getWLSourceAuditInfoByRunId(runId: String): Future[Either[ErrorResponse, WatchlistSourceAudit]] = {
    wlIn_houseIngestionDao.fetchWLSourceAuditInfoByRunId(runId).flatMap {
      recordOpt =>
        recordOpt match {
          case Some(record) => Future.successful(Right(record))
          case None => Future.successful(Left(ErrorResponses.watchlistSourceAuditNotFoundError))
        }
    }
  }

  override def getWLEntitiesAuditInfoByEntityId(entityId: String, fetchLastAudit: Boolean): Future[Either[ErrorResponse, Seq[WatchlistEntitiesAuditRecord]]] = {
    wlIn_houseIngestionDao.fetchWLEntitiesAuditInfoByEntityId(entityId, fetchLastAudit).flatMap {
      records =>
        (records.size > EMPTY_RECORD_COUNT) match {
          case true => Future.successful(Right(records))
          case false => Future.successful(Left(ErrorResponses.watchlistEntitiesAuditNotFoundError))
        }
    }
  }

  override def updateWlCurrentRunDetails(updateRequest: WLRunDetailsUpdateRequest): Future[Either[ErrorResponse, WLDataExtractionRunDetails]] = {
    updateRequest.status match {
      case WLDataExtractionStatus.TRANSFORMER_SUCCEEDED =>
        val ids = updateRequest.runId.split(RUN_ID_SOURCE_ID_SEPARATOR)
        val runId = ids(RUN_ID_ID_INDEX)
        val sourceId = ids(SOURCE_ID_INDEX).toInt
        wlIn_houseIngestionDao.fetchCurrentRunDetails(runId).flatMap {
          case Some(_) =>
            wlIn_houseIngestionDao.updateWLRunDetails(updateRequest.status, runId).flatMap {
              records =>
                (records > EMPTY_RECORD_COUNT) match {
                  case true => wlIn_houseIngestionDao.fetchCurrentRunDetails(runId).flatMap {
                    case Some(record) => Future.successful(Right(record))
                    case None => Future.successful(Left(ErrorResponses.UpdateRunDetailsError))
                  }
                  case false => Future.successful(Left(ErrorResponses.UpdateRunDetailsError))
                }
            }
          case None =>
            val wLDataExtractionRunDetailsRecord = WLDataExtractionRunDetails(
              id = 0L,
              runId = runId,
              sourceId = sourceId,
              status = updateRequest.status,
              createdAt = DateTime.now(),
              updatedAt = DateTime.now()
            )
            wlIn_houseIngestionDao.insertWLCurrentRunDetailsRecord(wLDataExtractionRunDetailsRecord).flatMap {
              case id: Long => Future.successful(Right(wLDataExtractionRunDetailsRecord.copy(id = id)))
              case _ => Future.successful(Left(ErrorResponses.UpdateRunDetailsError))
            }
        }
      case _ =>
        wlIn_houseIngestionDao.fetchCurrentRunDetails(updateRequest.runId).flatMap {
          case Some(_) =>
            wlIn_houseIngestionDao.updateWLRunDetails(updateRequest.status, updateRequest.runId).flatMap {
              records =>
                (records > EMPTY_RECORD_COUNT) match {
                  case true => wlIn_houseIngestionDao.fetchCurrentRunDetails(updateRequest.runId).flatMap {
                    case Some(record) => Future.successful(Right(record))
                    case None => Future.successful(Left(ErrorResponses.UpdateRunDetailsError))
                  }
                  case false => Future.successful(Left(ErrorResponses.UpdateRunDetailsError))
                }
            }
          case None =>
            val wLDataExtractionRunDetailsRecord = WLDataExtractionRunDetails(
              id = 0L,
              runId = updateRequest.runId,
              sourceId = updateRequest.sourceId.get,
              status = updateRequest.status,
              createdAt = DateTime.now(),
              updatedAt = DateTime.now()
            )
            wlIn_houseIngestionDao.insertWLCurrentRunDetailsRecord(wLDataExtractionRunDetailsRecord).flatMap {
              case id: Long => Future.successful(Right(wLDataExtractionRunDetailsRecord.copy(id = id)))
              case _ => Future.successful(Left(ErrorResponses.UpdateRunDetailsError))
            }
        }
    }
  }

  override def uploadFile(fileUploadRequest: FileUploadRequest, ingestionFile: FileItem): Future[Either[ErrorResponse, FileUploadResponse]] = {
    s3Service.uploadFile(fileUploadRequest.path, fileUploadRequest.fileName, ingestionFile).flatMap {
      case Right(status: Boolean) => Future.successful(Right(FileUploadResponse(status)))
      case _ => Future.successful(Left(ErrorResponses.S3FileUploadFailed))
    }
  }


  override def createTable(dynamoTableCreateRequest: DynamoTableCreateRequest): Future[Either[ErrorResponse,CreateTableResponse]] = {

    val keySchemaElement = KeySchemaElement.builder()
      .attributeName(DynamoTableKey)
      .keyType(KeyType.HASH)
      .build()

    val entityIdAttributeDefinition = AttributeDefinition.builder()
      .attributeName(DynamoTableKey)
      .attributeType(ScalarAttributeType.S)
      .build()

    val createTableRequest = CreateTableRequest
      .builder()
      .tableName(dynamoTableCreateRequest.tableName)
      .billingMode(BillingMode.PAY_PER_REQUEST)
      .keySchema(keySchemaElement)
      .attributeDefinitions(entityIdAttributeDefinition)
      .build()
    FutureConverters.toScala(dynamoDbAsyncClient.createTable(createTableRequest)) map {
      res => Right(res)
    } recoverWith {
      case ex: Exception =>
        logger.error(s"Exception while creating dynamo table ${ex}")
        Future.successful(Left(ErrorResponse(400, ex.getMessage)))
    }
  }

  override def deleteFiles(s3FilesDeleteRequest: S3FilesDeleteRequest): Future[Either[ErrorResponse, Set[String]]] = {
    Try{
      s3FilesDeleteRequest.filePaths.map(filePath => {
        logger.info(s"Deleting file ${filePath}")
        s3Service.deleteFile(filePath)
        logger.info(s"Deleted file ${filePath}")
      })
    } match {
      case Success(value) => Future.successful(Right(s3FilesDeleteRequest.filePaths))
      case Failure(ex) => Future.successful(Left(ErrorResponse(199, ex.getMessage)))
    }
  }

  override def getWLAuditInfoByDateRange(entitiesAuditFetchRequest: EntitiesAuditFetchRequest): Future[Either[ErrorResponse, List[WLInhouseAuditRecords]]] = {
    val offset: Option[Int] = (entitiesAuditFetchRequest.page, entitiesAuditFetchRequest.size) match {
      case (Some(page), Some(size)) => Some((page - 1) * size)
      case (_, _) => None
    }
    wlIn_houseIngestionDao.fetchAuditRecordsByDateRange(entitiesAuditFetchRequest.startDate, entitiesAuditFetchRequest.endDate, entitiesAuditFetchRequest.sourceId, entitiesAuditFetchRequest.entitySourceChangeType, entitiesAuditFetchRequest.runId, offset, entitiesAuditFetchRequest.size).flatMap {
      results =>
        if (results.nonEmpty) {
          logger.info(s"Successfully fetched ${results.size} audit records by date range for startDate: ${entitiesAuditFetchRequest.startDate} and endDate: ${entitiesAuditFetchRequest.endDate}")
          Future.successful(Right(results))
        } else {
          logger.warn(s"Empty records for date range. StartDate: ${entitiesAuditFetchRequest.startDate} , EndDate: ${entitiesAuditFetchRequest.endDate}")
          Future.successful(Right(results))
        }
    }.recover {
      case ex: Throwable =>
        logger.error(s"Error while fetching audit records by date range. StartDate: ${entitiesAuditFetchRequest.startDate} , EndDate: ${entitiesAuditFetchRequest.endDate} error: ${ex.getMessage}", ex)
        Left(ErrorResponses.FetchAuditRecordsByDateRangeError)
    }
  }

  override def getWatchlistSources(): Future[Either[ErrorResponse, Set[WatchlistSource]]] = {
    val records = WatchlistSources.getAll
    if(records.size> EMPTY_RECORD_COUNT) {
      Future.successful(Right(records))
    }
    else {
      Future.successful(Left(ErrorResponses.watchlistAuditLogInfoNotFoundError))
    }
  }
}
