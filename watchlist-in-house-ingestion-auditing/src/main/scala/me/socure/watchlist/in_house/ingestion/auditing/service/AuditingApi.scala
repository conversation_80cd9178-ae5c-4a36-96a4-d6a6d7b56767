package me.socure.watchlist.in_house.ingestion.auditing.service

import me.socure.model.ErrorResponse
import me.socure.watchlist.in_house.ingestion.common.models.enums.WatchlistSources.WatchlistSource
import me.socure.watchlist.in_house.ingestion.common.models.request.{DynamoTableCreateRequest, EntitiesAuditFetchRequest, FileUploadRequest, S3FilesDeleteRequest, WLRunDetailsUpdateRequest}
import me.socure.watchlist.in_house.ingestion.common.models.response.{FileUploadResponse, WatchlistDataExtractionRunDetailsSuccessResponse, WatchlistEntitiesAuditSuccessResponse, WatchlistEntitySourceMappingSuccessResponse, WatchlistSourceAuditSuccessResponse, WatchlistSuppressedEntitiesSuccessResponse}
import me.socure.watchlist.in_house.ingestion.common.models.{WLDataExtractionRunDetails, WLInhouseAuditRecords, WatchlistEntitiesAudit, WatchlistEntitiesAuditRecord, WatchlistEntitySourceMapping, WatchlistSourceAudit, WatchlistSuppressedEntities}
import org.scalatra.servlet.FileItem
import org.joda.time.DateTime
import software.amazon.awssdk.services.dynamodb.model.CreateTableResponse

import scala.concurrent.Future

/**
 * Created by Soorya Prasad on Aug 23, 2023
 */
trait AuditingApi {
  def auditWLSource(watchlistSourceAudit: WatchlistSourceAudit): Future[Either[ErrorResponse, WatchlistSourceAuditSuccessResponse]]

  def auditWLEntities(watchlistEntitiesAudit: WatchlistEntitiesAudit): Future[Either[ErrorResponse, WatchlistEntitiesAuditSuccessResponse]]

  def auditWLSuppressedEntities(watchlistSuppressedEntities: WatchlistSuppressedEntities): Future[Either[ErrorResponse, WatchlistSuppressedEntitiesSuccessResponse]]

  def auditWLDataExtractionRunDetails(wlDataExtractionRunDetails: WLDataExtractionRunDetails): Future[Either[ErrorResponse, WatchlistDataExtractionRunDetailsSuccessResponse]]

  def auditWLEntitySourceMapping(watchlistEntitySourceMapping: WatchlistEntitySourceMapping): Future[Either[ErrorResponse, WatchlistEntitySourceMappingSuccessResponse]]

  def getWLSourceAuditInfoBySourceId(sourceId: Int): Future[Either[ErrorResponse, Seq[WatchlistSourceAudit]]]

  def getWLSourceAuditInfoByRunId(runId: String): Future[Either[ErrorResponse, WatchlistSourceAudit]]

  def getWLEntitiesAuditInfoByEntityId(entityId: String, fetchLastAudit: Boolean): Future[Either[ErrorResponse, Seq[WatchlistEntitiesAuditRecord]]]

  def updateWlCurrentRunDetails(updateRequest: WLRunDetailsUpdateRequest): Future[Either[ErrorResponse, WLDataExtractionRunDetails]]

  def uploadFile(fileUploadRequest: FileUploadRequest, ingestionFile: FileItem): Future[Either[ErrorResponse, FileUploadResponse]]

  def createTable(dynamoTableCreateRequest: DynamoTableCreateRequest): Future[Either[ErrorResponse,CreateTableResponse]]

  def deleteFiles(s3FilesDeleteRequest: S3FilesDeleteRequest): Future[Either[ErrorResponse, Set[String]]]

  def getWLAuditInfoByDateRange(entitiesAuditFetchRequest: EntitiesAuditFetchRequest): Future[Either[ErrorResponse,List[WLInhouseAuditRecords]]]

  def getWatchlistSources(): Future[Either[ErrorResponse,Set[WatchlistSource]]]
}
